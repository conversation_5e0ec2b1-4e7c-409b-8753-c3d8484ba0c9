# Monitoring Structural Integrity Using Machine Learning and Remotely Sensed Imagery 🏗️📊

3D inspection of structures using point cloud analysis for geometric measurement and anomaly detection. This project combines point cloud alignment, plane detection, geometric analysis, and machine learning to detect anomalies in structural installations.

[![Tests](https://github.com/balijepalli/solar-array-inspection-3d/actions/workflows/tests.yml/badge.svg)](https://github.com/balijepalli/solar-array-inspection-3d/actions/workflows/tests.yml)
[![Documentation](https://github.com/balijepalli/solar-array-inspection-3d/actions/workflows/docs.yml/badge.svg)](https://github.com/balijepalli/solar-array-inspection-3d/actions/workflows/docs.yml)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

> **Note:** This project was previously known as "Solar Array Inspection 3D" / "GeoPoseNet" / "pc-alignment-benchmark".

## 🔍 Overview

This project provides tools for analyzing solar panel installations using 3D point cloud data, with a focus on:

1. **Point Cloud Alignment**: Robust alignment of point clouds using ICP, neural networks, and hybrid approaches
2. **Plane Detection**: Identification of solar panels using RANSAC and DBSCAN clustering
3. **Geometric Analysis**: Extraction of tilt angles, spacing, and other geometric features
4. **Anomaly Detection**: Machine learning models to identify irregularities in solar panel installations
5. **Visualization**: 3D visualization and heatmaps for intuitive inspection

## ✨ Features

- 🔄 **Multiple Alignment Methods**: ICP, Neural Network, and Hybrid approaches for point cloud alignment
- 🔍 **Plane Detection**: RANSAC algorithm for detecting planes and DBSCAN clustering for segmenting individual panels
- 📏 **Geometric Analysis**: Calculation of panel orientation, dimensions, spatial relationships, and other geometric features
- 🤖 **ML Classification**: Anomaly detection for identifying irregular installations
- 📈 **3D Visualization**: Interactive visualization with heatmaps and annotations
- 📁 **Format Support**: Works with DWG, IFC, LAS, PLY, OBJ, and more
- 🔄 **Data Conversion**: Tools for converting between CAD formats (DWG to IFC) and generating point clouds

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/balijepalli/solar-array-inspection-3d.git
cd solar-array-inspection-3d

# Install dependencies
pip install -r requirements.txt

# Install the package in development mode
pip install -e .
```

### Basic Usage

```python
from solar_panel_analyzer import alignment, plane_detection, visualization

# Load point clouds
source_cloud = load_point_cloud("data/sample/source.obj")
target_cloud = load_point_cloud("data/sample/target.las")

# Align point clouds
aligned_cloud, transformation = alignment.align_point_clouds_hybrid(source_cloud, target_cloud)

# Detect planes using RANSAC
planes = plane_detection.detect_planes_ransac(aligned_cloud, distance_threshold=0.01)

# Segment planes into individual panels using DBSCAN
panels = plane_detection.segment_panels_dbscan(planes, eps=0.1, min_samples=10)

# Visualize results
visualization.visualize_panels(aligned_cloud, panels)
```

For more examples, see the [examples directory](examples/) or the [documentation](docs/).

## 📚 Documentation

Comprehensive documentation is available in the `docs/` directory:

### User Guides
- [Installation Guide](docs/user_guides/installation.md)
- [Quickstart Guide](docs/user_guides/quickstart.md)
- [Example Usage](docs/user_guides/examples.md)
- [Plane Detection with RANSAC](docs/user_guides/plane_detection_with_ransac.md)
- [Panel Segmentation with DBSCAN](docs/user_guides/panel_segmentation_with_dbscan.md)
- [Geometric Analysis Guide](docs/user_guides/geometric_analysis_guide.md)

### Technical Documentation
- [Preprocessing](docs/technical/preprocessing/)
  - [DWG to IFC Conversion](docs/technical/preprocessing/dwg_to_ifc_conversion_guide.md)
  - [IFC to Point Cloud Comparison](docs/technical/preprocessing/ifc_to_pointcloud_comparison_guide.md)
- [Alignment Methods](docs/technical/alignment/overview.md)
  - [ICP Implementation](docs/technical/alignment/icp.md)
  - [Neural Network Approach](docs/technical/alignment/neural_network.md)
  - [Hybrid Method](docs/technical/alignment/hybrid.md)
  - [Rotation Representations](docs/technical/alignment/rotation_representations.md)
- [Plane Detection](docs/technical/plane_detection/)
  - [RANSAC Implementation](docs/technical/plane_detection/ransac.md)
  - [DBSCAN Clustering](docs/technical/plane_detection/dbscan.md)
- [Geometric Analysis](docs/technical/geometric_analysis/overview.md)
- [Anomaly Detection](docs/technical/classification/anomaly_detection.md)

### Research Documentation
- [Project Proposal](docs/project_proposal/abstract.md)
- [Research Methodology](docs/research/methodology.md)
- [Experiment Design](docs/research/experiments.md)
- [Results and Analysis](docs/research/results.md)

## 📊 Research

This project includes research components for academic publication:

### Project Proposal

This project is being developed as part of academic research. The detailed project proposal is available at:
- [Project Work Outline](docs/project_proposal/abstract.md) - Comprehensive outline with objectives, methodology, and timeline

### Papers

- **[Point Cloud Alignment Benchmark: ICP, Neural Network, and Hybrid Methods]()** - Comparative analysis of point cloud alignment methods for solar panel inspection
  - [Experiment Code](paper/experiments/alignment_benchmark.py)
  - [Results](paper/results/alignment_results.csv)

### Benchmarks

The `paper/benchmark/` directory contains benchmark implementations and datasets for evaluating:
- Point cloud alignment methods
- Plane detection algorithms
- Solar panel anomaly detection

## 📂 Project Structure

```
solar-array-inspection-3d/
├── src/                  # Source code
│   ├── alignment/        # Point cloud alignment
│   ├── plane_detection/  # RANSAC and DBSCAN
│   ├── geometric_analysis/ # Feature extraction
│   ├── classification/   # ML models
│   └── visualization/    # 3D visualization
├── paper/                # Paper-specific materials
├── notebooks/            # Research notebooks
├── examples/             # Example scripts
├── tests/                # Test suite
├── docs/                 # Documentation
└── data/                 # Sample data
```

See the [repository structure documentation](docs/development/repository_structure.md) for more details.

## 🧪 Development

### Testing

```bash
# Run all tests
pytest

# Run specific test module
pytest tests/test_alignment.py
```

### Contributing

Contributions are welcome! Please see our [Contributing Guide](docs/development/contributing.md) for more information.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- This project builds upon research in point cloud processing, computer vision, and machine learning
- Special thanks to contributors and researchers in the field

## 📝 Citation

If you find this work useful in your research, please consider citing:

```bibtex
@misc{structuralmonitoring2025,
  title={Monitoring Structural Integrity Using Machine Learning and Remotely Sensed Imagery},
  author={Balijepalli, Preetam and Kumar, Manohar C V S S},
  year={2025},
  url={https://github.com/balijepalli/solar-array-inspection-3d}
}
```

## 📬 Contact

- **Author**: Preetam Balijepalli
- **Email**: <EMAIL>
- **GitHub**: [balijepalli](https://github.com/balijepalli)
- **Reviewer**: Dr. Manohar Kumar C V S S (<EMAIL>)
