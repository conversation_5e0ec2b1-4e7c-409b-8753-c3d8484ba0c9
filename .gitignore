# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# TensorFlow
logs/
saved_models/
checkpoints/

# Project specific
results/
data/*.obj
data/*.las
data/*.ply
data/*.pcd

# Generated PDFs
docs/project_proposal/*.pdf

# Jupyter Notebooks
.ipynb_checkpoints
notebooks/.ipynb_checkpoints/

# OS specific
.DS_Store
.env
.venv
venv/
ENV/
pdf_env/

# IDE
.idea/
.vscode/
*.swp
*.swo
