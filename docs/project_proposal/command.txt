# Commands for generating PDF from Markdown

## Basic PDF Generation
pandoc docs/project_proposal/abstract.md -o docs/project_proposal/abstract.pdf --pdf-engine=xelatex -V geometry:"margin=1in" -V fontsize=12pt -V mainfont="Helvetica" -V colorlinks=true

## Academic Style PDF with Times New Roman
pandoc docs/project_proposal/abstract.md -o docs/project_proposal/abstract_academic.pdf --pdf-engine=xelatex -V documentclass=article -V papersize=a4 -V geometry:"margin=1in" -V fontsize=11pt -V mainfont="Times New Roman" -V colorlinks=true -V linkcolor=blue -V urlcolor=blue -V toccolor=blue --toc

## Academic Style PDF with Title
pandoc docs/project_proposal/abstract.md -o docs/project_proposal/abstract_academic.pdf --pdf-engine=xelatex -V documentclass=article -V papersize=a4 -V geometry:"margin=1in" -V fontsize=11pt -V mainfont="Times New Roman" -V colorlinks=true -V linkcolor=blue -V urlcolor=blue -V toccolor=blue --toc -V title="Monitoring Structural Integrity Using Machine Learning and Remotely Sensed Imagery" -V author="Preetam Balijepalli" -V date="May 2025"

## Using Simple Custom Template (requires LaTeX packages)
# First install required LaTeX packages:
# tlmgr install titling fancyhdr

# Then run:
pandoc docs/project_proposal/abstract.md -o docs/project_proposal/abstract_academic.pdf --pdf-engine=xelatex --template=docs/project_proposal/templates/template.tex

## Using BITS Dissertation Template (requires LaTeX packages and BITS logo)
# First install required LaTeX packages:
# tlmgr install titling fancyhdr setspace xcolor

# Place BITS logo at docs/project_proposal/templates/bits_logo.png
# Then run:
pandoc docs/project_proposal/abstract.md -o docs/project_proposal/dissertation_abstract.pdf --pdf-engine=xelatex --template=docs/project_proposal/templates/dissertation_template.tex

## Requirements
# 1. Pandoc: brew install pandoc
# 2. LaTeX: brew install --cask mactex
# 3. Python packages: pip install markdown-pdf mdpdf grip

## Virtual Environment Setup
# python -m venv pdf_env
# source pdf_env/bin/activate
# pip install markdown-pdf mdpdf grip
