# Project Proposal Documentation

This directory contains the project proposal documentation for the "Monitoring Structural Integrity Using Machine Learning and Remotely Sensed Imagery" project.

## Files

- `abstract.md`: The main content of the project proposal in Markdown format
- `command.txt`: Commands for generating PDF versions of the proposal
- `templates/`: LaTeX templates for PDF generation
  - `template.tex`: Simple academic paper template
  - `dissertation_template.tex`: BITS <PERSON>lani dissertation-style template

## Generating PDFs

To generate a PDF version of the proposal, use one of the commands in `command.txt`. For example:

```bash
# Activate the virtual environment
source ../../pdf_env/bin/activate

# Generate a basic PDF
pandoc abstract.md -o abstract.pdf --pdf-engine=xelatex -V geometry:"margin=1in" -V fontsize=12pt -V mainfont="Helvetica" -V colorlinks=true

# Generate a dissertation-style PDF (requires BITS logo)
pandoc abstract.md -o dissertation_abstract.pdf --pdf-engine=xelatex --template=templates/dissertation_template.tex
```

## Requirements

- Pandoc: `brew install pandoc`
- LaTeX: `brew install --cask mactex`
- Python packages: `pip install markdown-pdf mdpdf grip`

## Note

The generated PDF files are not tracked by Git. If you need to share the PDF, please generate it using the commands provided.
