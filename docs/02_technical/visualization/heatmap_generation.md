# Heatmap Generation

*This document is a placeholder for future documentation on heatmap generation for visualizing anomalies in solar panel installations.*

## Overview

Heatmaps provide an intuitive visual representation of anomaly scores or other metrics across solar panel installations. This document outlines methods for generating and visualizing heatmaps on 3D point cloud data.

## Planned Implementation

### Heatmap Types

1. **Anomaly Score Heatmaps**:
   - Visualize anomaly scores for each panel
   - Color gradient from normal (green) to anomalous (red)
   - Highlight panels exceeding threshold scores

2. **Feature Deviation Heatmaps**:
   - Show deviation from expected values for specific features
   - Separate heatmaps for tilt, spacing, orientation, etc.
   - Useful for identifying specific types of anomalies

3. **Confidence Heatmaps**:
   - Visualize confidence levels in measurements or classifications
   - Identify areas with uncertain data or predictions
   - Guide further inspection or data collection

### Implementation Approaches

1. **Panel-level Heatmaps**:
   - Assign colors to entire panel planes
   - Simple implementation with clear visual boundaries
   - Limited spatial resolution within panels

2. **Point-level Heatmaps**:
   - Assign colors to individual points in the cloud
   - Higher resolution but more computationally intensive
   - Better for visualizing local variations

3. **Surface Interpolation**:
   - Interpolate values between measured points
   - Create smooth color gradients across surfaces
   - Visually appealing but may obscure discrete boundaries

### Technical Implementation

1. Map scalar values (anomaly scores, deviations) to color scale
2. Apply colors to point cloud or panel surfaces
3. Implement interactive controls for threshold adjustment
4. Provide legend and scale information
5. Enable toggling between different heatmap types

## Integration with Other Components

- Input: Anomaly detection results and feature measurements
- Output: Colored visualization of point cloud or panels
- User interaction: Adjustable thresholds and visualization options

## Evaluation Criteria

- Visual clarity and interpretability
- Computational efficiency for interactive use
- Effectiveness in highlighting anomalies
- User feedback on usefulness

## References

1. Aigner, W., Miksch, S., Schumann, H., & Tominski, C. (2011). Visualization of time-oriented data. Springer Science & Business Media.

2. Wilkinson, L., & Friendly, M. (2009). The history of the cluster heat map. The American Statistician, 63(2), 179-184.

3. Mayorga, A., & Gleicher, M. (2013). Splatterplots: Overcoming overdraw in scatter plots. IEEE Transactions on Visualization and Computer Graphics, 19(9), 1526-1538.

## TODO

- [ ] Implement color mapping functions for scalar values
- [ ] Develop panel-level coloring for basic heatmaps
- [ ] Create interactive controls for threshold adjustment
- [ ] Integrate with anomaly detection results
- [ ] Add legends and explanatory elements
- [ ] Implement point-level heatmaps for detailed visualization
