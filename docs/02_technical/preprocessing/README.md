# Preprocessing Technical Documentation

This directory contains technical documentation for preprocessing and converting various data formats used in the 3D energy inspection project.

## Overview

Preprocessing is a critical step in the 3D inspection pipeline, involving the conversion and preparation of data from different sources, such as CAD models (DWG, DXF), Building Information Models (IFC), and point clouds. These conversions and metadata extraction processes allow for comparison between design data and as-built measurements.

## Documentation

### CAD Format Conversion

- [DWG to IFC Conversion Guide](dwg_to_ifc_conversion_guide.md)
  - Installing FreeCAD and ODA File Converter
  - Converting DWG files to IFC format
  - Configuring optimal export settings
  - Troubleshooting common issues

### Point Cloud Generation and Comparison

- [IFC to Point Cloud Comparison Guide](ifc_to_pointcloud_comparison_guide.md)
  - Converting IFC models to point clouds
  - Methods for comparing point clouds
  - Visualization and analysis techniques
  - Best practices for meaningful comparisons

### Metadata Extraction

- [BIM and CAD Metadata Extraction Guide](metadata_extraction_guide.md)
  - Extracting structured data from IFC models
  - Processing CAD files with advanced geometric analysis
  - Coordinate transformations between local and global systems
  - Using CADQuery for enhanced 3D model analysis
  - Integration of metadata with alignment and analysis pipelines

## Workflow

The typical preprocessing workflow involves:

1. **CAD Model Acquisition**: Obtaining DWG/DXF files from engineering or design teams
2. **Format Conversion**: Converting CAD formats to IFC for better interoperability
3. **Metadata Extraction**: Extracting structured data from IFC and CAD files
4. **Point Cloud Generation**: Creating point clouds from the IFC models
5. **Alignment**: Aligning generated point clouds with scan data using ICP algorithm
6. **Comparison**: Analyzing differences between as-designed and as-built geometries

## Related Notebooks

The following Jupyter notebooks implement these preprocessing techniques:

- `notebooks/preprocessing/simple_dwg_to_obj_converter.ipynb`: Converts DWG files to OBJ format
- `notebooks/preprocessing/ifc_to_pointcloud_converter.ipynb`: Converts IFC models to point clouds
- `notebooks/preprocessing/extract_ifc_metadata.ipynb`: Extracts structured metadata from IFC models
- `notebooks/preprocessing/extract_cad_metadata.ipynb`: Extracts metadata from CAD files with CADQuery
- `notebooks/alignment/icp_pc_alignment.ipynb`: Aligns point clouds using Iterative Closest Point algorithm

## Best Practices

- Ensure consistent scale and units across all conversions
- Validate models at each conversion step
- Document settings used for each conversion
- Consider filtering or simplifying complex models for better performance
- Back up original files before conversion
- Document coordinate reference systems (CRS) and transformation parameters
- Use appropriate ICP parameters (iterations, tolerance) based on point cloud size and complexity
- Verify alignment quality using quantitative metrics (RMSE, transformation matrix)
- Create a structured metadata schema for consistent property extraction
- Validate extracted metadata against original design documents