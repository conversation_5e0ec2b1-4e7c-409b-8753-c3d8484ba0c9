# DBSCAN Clustering for Solar Panel Segmentation

This document provides a detailed explanation of the Density-Based Spatial Clustering of Applications with Noise (DBSCAN) algorithm implementation for segmenting detected planes into individual solar panels, as implemented in the `notebooks/plane_detection/dbscan_clustering.ipynb` notebook.

## Overview

DBSCAN is a density-based clustering algorithm that groups together points that are closely packed together, marking as outliers points that lie alone in low-density regions. In our context, DBSCAN is used to segment planes detected by RANSAC into individual solar panels.

The algorithm works by identifying dense regions of points separated by regions of lower density. This makes it particularly suitable for solar panel segmentation, as panels typically form distinct clusters on a plane with gaps between them.

## Algorithm Description

The DBSCAN algorithm consists of the following steps:

1. **Neighborhood Identification**: For each point, identify all points within a specified distance (eps)
2. **Core Point Identification**: Points with at least a minimum number of neighbors (min_samples) are labeled as core points
3. **Cluster Formation**: Core points that are neighbors form clusters
4. **Expansion**: Non-core points that are neighbors of core points are added to the clusters
5. **Noise Identification**: Points that are not core points and not neighbors of any core point are labeled as noise

## Implementation Details

### Parameter Estimation

One of the key challenges in using DBSCAN is selecting appropriate values for the `eps` and `min_samples` parameters. Our implementation includes a method to estimate a good `eps` value based on the k-distance graph:

1. Compute the distance to the k-th nearest neighbor for each point
2. Sort these distances in ascending order
3. Plot the sorted distances
4. Find the "elbow point" in the curve, which indicates a good value for `eps`

### Feature Vector Construction

To improve clustering results, our implementation uses both spatial coordinates and normal vectors as features:

1. Compute normal vectors for each point using PCA
2. Create a feature vector by concatenating weighted spatial coordinates and normal vectors
3. Standardize the features to ensure equal importance
4. Apply DBSCAN to the standardized feature vectors

### Multiple Plane Processing

The implementation processes each plane detected by RANSAC separately:

1. Load the plane point cloud
2. Estimate DBSCAN parameters for the specific plane
3. Apply DBSCAN clustering to segment the plane
4. Process each cluster to extract geometric properties
5. Combine results from all planes

## Key Parameters

- **eps**: Maximum distance between two samples for them to be considered as in the same neighborhood
- **min_samples**: Minimum number of samples in a neighborhood for a point to be considered as a core point
- **normal_weight**: Weight for normal vectors in the feature vector (0-1)
- **use_normals**: Whether to use normal vectors for clustering

## Geometric Property Calculation

For each identified cluster (potential solar panel), the implementation calculates various geometric properties:

1. **Centroid**: The center point of the cluster
2. **Normal Vector**: The orientation of the panel
3. **Dimensions**: Length and width of the panel
4. **Area**: Surface area of the panel
5. **Tilt Angle**: Angle between the panel normal and the vertical axis
6. **Azimuth**: Compass direction that the panel faces

These properties are calculated using Principal Component Analysis (PCA):

1. Compute the covariance matrix of the centered points
2. Perform eigendecomposition to find the principal axes
3. The first two eigenvectors define the plane of the panel
4. The third eigenvector (smallest eigenvalue) is the normal vector
5. Project points onto the plane to calculate dimensions

## Visualization

The implementation includes visualization capabilities using both Open3D and Matplotlib:

- **Open3D Visualization**: Interactive 3D visualization of clusters with different colors
- **Matplotlib Visualization**: Static plots of clusters for documentation

## Integration with RANSAC

The DBSCAN clustering is designed to work seamlessly with the RANSAC plane detection:

1. RANSAC detects planes in the point cloud
2. DBSCAN segments each plane into individual panels
3. Geometric properties are calculated for each panel
4. Results are saved for further analysis

This two-step approach leverages the strengths of both algorithms:
- RANSAC is effective at identifying planar surfaces
- DBSCAN is effective at segmenting these surfaces into distinct objects

## Optimization Strategies

- **Adaptive Parameter Selection**: Parameters are adjusted based on the characteristics of each plane
- **Feature Weighting**: Balancing the importance of spatial coordinates and normal vectors
- **Noise Handling**: Filtering out noise points to improve clustering quality
- **Minimum Cluster Size**: Filtering out small clusters that are unlikely to be valid panels

## Applications

The segmented panels and their geometric properties can be used for various applications:

1. **Anomaly Detection**: Identifying panels with unusual orientation or dimensions
2. **Layout Analysis**: Analyzing the arrangement and spacing of panels
3. **Performance Optimization**: Evaluating the orientation of panels for optimal solar exposure
4. **Damage Assessment**: Identifying missing or damaged panels

## References

1. Ester, M., Kriegel, H. P., Sander, J., & Xu, X. (1996). A density-based algorithm for discovering clusters in large spatial databases with noise. In Proceedings of the 2nd International Conference on Knowledge Discovery and Data Mining (KDD-96) (pp. 226-231).

2. Schubert, E., Sander, J., Ester, M., Kriegel, H. P., & Xu, X. (2017). DBSCAN revisited, revisited: why and how you should (still) use DBSCAN. ACM Transactions on Database Systems (TODS), 42(3), 1-21.
