# Geometric Analysis of Solar Panels

This document provides a detailed explanation of the geometric analysis implementation for solar panels detected in point cloud data, as implemented in the `notebooks/geometric_analysis/panel_geometry_analysis.ipynb` notebook.

## Overview

Geometric analysis is a critical step in the solar panel inspection pipeline. After detecting and segmenting individual panels using RANSAC and DBSCAN, geometric analysis extracts key properties such as tilt angle, azimuth angle, dimensions, and spacing. These properties are essential for assessing the quality of the installation, detecting anomalies, and optimizing solar energy production.

## Key Geometric Properties

### Panel Orientation

1. **Tilt Angle**: The angle between the panel surface and the horizontal plane. A tilt angle of 0° indicates a horizontal panel, while 90° indicates a vertical panel.

2. **Azimuth Angle**: The compass direction that the panel faces. An azimuth of 0° indicates north-facing, 90° indicates east-facing, 180° indicates south-facing, and 270° indicates west-facing.

### Panel Dimensions

1. **Length**: The longest dimension of the panel.

2. **Width**: The shortest dimension of the panel.

3. **Area**: The surface area of the panel.

### Panel Layout

1. **Row and Column Organization**: Panels are grouped into rows and columns based on their spatial arrangement.

2. **Row Spacing**: The distance between adjacent rows of panels.

3. **Column Spacing**: The distance between adjacent panels within the same row.

## Implementation Details

### Panel Dimension Calculation

The panel dimensions are calculated using the following steps:

1. Project the 3D points onto the panel plane
2. Compute the 2D convex hull of the projected points
3. Calculate the area of the convex hull
4. Use Principal Component Analysis (PCA) to find the principal axes of the panel
5. Compute the length and width along these principal axes

### Tilt and Azimuth Calculation

The tilt and azimuth angles are calculated from the panel normal vector:

1. **Tilt Angle**: Calculated as the angle between the panel normal vector and the vertical (z-axis)
2. **Azimuth Angle**: Calculated as the angle between the projection of the normal vector onto the horizontal plane and the north direction (y-axis in our coordinate system)

### Panel Grouping

Panels are grouped into rows and columns using the following approach:

1. **Row Grouping**: Hierarchical clustering is applied to the y-coordinates of panel centroids
2. **Column Ordering**: Within each row, panels are ordered based on their x-coordinates

### Spacing Calculation

The spacing between panels is calculated as follows:

1. **Row Spacing**: The distance between the centroids of adjacent rows
2. **Column Spacing**: The distance between the centroids of adjacent panels within the same row

## Key Algorithms

### Principal Component Analysis (PCA)

PCA is used to find the principal axes of the panel, which correspond to the length and width directions. The steps are:

1. Center the points by subtracting their mean
2. Compute the covariance matrix
3. Perform eigendecomposition to find the principal components
4. The first two principal components define the panel plane
5. The third principal component (with the smallest eigenvalue) is the normal vector

### Hierarchical Clustering

Hierarchical clustering is used to group panels into rows based on their y-coordinates:

1. Compute the linkage matrix using Ward's method
2. Apply a distance threshold to form clusters
3. Assign row labels to each panel based on the clustering result

## Integration with Pipeline

The geometric analysis module integrates with the rest of the pipeline as follows:

1. **Input**: Segmented panels from the DBSCAN clustering stage
2. **Processing**: Calculation of geometric properties
3. **Output**: DataFrame containing panel properties and spatial relationships
4. **Next Step**: Anomaly detection based on geometric properties

## Visualization Capabilities

The implementation includes several visualization functions:

1. **Property Visualization**: 3D scatter plots with color coding based on properties like tilt, azimuth, or area
2. **Normal Vector Visualization**: 3D quiver plots showing the orientation of each panel
3. **Row and Column Visualization**: 3D scatter plots with color coding based on row assignment

## Statistical Analysis

The implementation includes statistical analysis of panel properties:

1. **Summary Statistics**: Mean, standard deviation, min, max, etc. for key properties
2. **Group Statistics**: Statistics grouped by row or column
3. **Distribution Visualization**: Histograms and scatter plots of key properties

## Applications

The geometric analysis results can be used for various applications:

1. **Quality Control**: Verifying that panels are installed at the correct tilt and azimuth angles
2. **Anomaly Detection**: Identifying panels with unusual orientation or dimensions
3. **Layout Optimization**: Analyzing panel spacing to optimize land use and minimize shading
4. **Performance Prediction**: Using orientation data to predict solar energy production

## References

1. Jochem, A., Höfle, B., Rutzinger, M., & Pfeifer, N. (2009). Automatic roof plane detection and analysis in airborne lidar point clouds for solar potential assessment. Sensors, 9(7), 5241-5262.

2. Huang, H., Brenner, C., & Sester, M. (2013). A generative statistical approach to automatic 3D building roof reconstruction from laser scanning data. ISPRS Journal of Photogrammetry and Remote Sensing, 79, 29-43.
