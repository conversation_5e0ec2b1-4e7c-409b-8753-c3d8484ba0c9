# Spacing Analysis

*This document is a placeholder for future documentation on spacing analysis between solar panels.*

## Overview

Proper spacing between solar panels is crucial for optimal energy production and maintenance access. This document outlines methods for measuring and analyzing the spacing between detected solar panels in point cloud data.

## Planned Implementation

### Measurement Approaches

1. **Centroid-based Spacing**:
   - Calculate the centroid of each detected panel
   - Measure the Euclidean distance between centroids
   - Account for panel dimensions to determine edge-to-edge spacing

2. **Boundary-based Spacing**:
   - Extract the boundary points of each panel
   - Compute the minimum distance between boundaries of adjacent panels
   - Handle irregular panel shapes and orientations

3. **Projection-based Spacing**:
   - Project panels onto a common plane (e.g., ground plane)
   - Measure spacing in the projected space
   - Useful for panels at different heights or tilts

### Implementation Steps

1. Identify adjacent panel pairs based on proximity
2. Select appropriate measurement method based on panel configuration
3. Calculate spacing measurements
4. Validate against expected spacing standards
5. Flag anomalous spacing for further inspection

### Handling Special Cases

- Irregular panel arrangements
- Multi-level installations
- Partial occlusions in the point cloud

## Integration with Other Components

- Input: Detected and clustered planes from RANSAC and DBSCAN
- Output: Spacing measurements between adjacent panels
- Next step: Feature vector creation for ML classification

## Evaluation Metrics

- Accuracy compared to design specifications
- Consistency across similar panel pairs
- Robustness to noise and missing data

## References

1. Díaz-Dorado, E., Suárez-García, A., Carrillo, C. J., & Cidrás, J. (2017). Optimal distribution for photovoltaic solar trackers to minimize power losses caused by shadows. Renewable Energy, 97, 129-143.

2. Weinmann, M., Jutzi, B., & Mallet, C. (2014). Semantic 3D scene interpretation: A framework combining optimal neighborhood size selection with relevant features. ISPRS Annals of the Photogrammetry, Remote Sensing and Spatial Information Sciences, 2(3), 181.

## TODO

- [ ] Implement centroid calculation for detected panels
- [ ] Develop spacing measurement algorithms
- [ ] Create validation methods for spacing measurements
- [ ] Integrate with plane detection pipeline
- [ ] Visualize spacing measurements on 3D point cloud
