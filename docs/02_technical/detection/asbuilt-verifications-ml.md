---
marp: true
title: Monitoring Structural Integrity Using Machine Learning and Remotely Sensed Imagery
theme: default
paginate: true
size: 16:9
---

# Monitoring Structural Integrity  
### Using Machine Learning and Remotely Sensed Imagery

**Preetam Balijepalli**  
**BITS ID:** 2023AB05075  

**Program:** M.Tech (AI & ML) | Semester IV | May 2025 – Aug/Sep 2025  
**Institute:** BITS Pilani, Rajasthan  

---

## Supervisor & Reviewer

- **Supervisor:** Bhaskar R  
  - <PERSON><PERSON>Sc (Engg), B.E. Mechanical Engineering  
  - CEO, Datasee.AI Pvt. Ltd., Chennai

- **Reviewer:** Dr. <PERSON> V S S  
  - PhD Remote Sensing & Image Processing  
  - Assistant Professor, Dept. of Space Science & Technology, Central University of Andhra Pradesh


---

## Research Focus & Objectives

**Primary Goal:**  
Develop and evaluate an automated structural compliance system for solar site foundations using comparative analysis of ML and traditional methods.

---

## Core Research Questions

- How does **PointNet-based alignment** compare to **traditional ICP** for CAD/IFC registration?
- Which ground segmentation method (**CSF vs RANSAC vs DBSCAN**) performs best for solar construction sites?
- Can **deep learning methods** (PointNet++/DGCNN) outperform traditional geometric approaches for I-section pile segmentation?
- What is the optimal pipeline for **automated geometric compliance validation**?

---

## Proposed Methodology  
### Multi-Method Comparative Framework

---

## Stage 1: IFC Alignment Evaluation

**Methods to Compare:**
- **PointNet-based Registration:** Learning-based alignment using neural networks
- **Traditional ICP:** Iterative closest point with optional FPFH features

**Evaluation Criteria:**
- Registration accuracy (RMS Error)
- Processing time & computational efficiency
- Robustness to noise and partial data
- Scalability across different site conditions

---

## Stage 2: Ground Segmentation Comparison

**Methods to Evaluate:**
- **CSF (Cloth Simulation Filter):** Physics-based terrain extraction
- **RANSAC + Plane Fitting:** Mathematical plane detection with outlier rejection
- **DBSCAN Clustering:** Density-based spatial clustering

**Comparative Metrics:**
- Segmentation accuracy against ground truth
- Computational performance
- Robustness to terrain variations
- Parameter sensitivity analysis

---

## Stage 3: Point Cloud Classification

**Research Question:** Deep Learning vs Traditional Geometric Methods

**Deep Learning Candidates:**
- **PointNet++:** Hierarchical feature learning for point clouds
- **DGCNN:** Dynamic graph convolution networks

**Baseline Comparison:**
- Traditional geometric fitting methods
- Rule-based classification approaches

**Current Status:** 🧪 Architecture benchmarking in progress

---

## Geometric Analysis Pipeline

**Post-Classification Measurements:**
- **Pile Positioning:** Deviation from IFC coordinates
- **Verticality Analysis:** Angular deviation measurement  
- **Spacing Validation:** Inter-pile distance compliance
- **Geometric Compliance:** Tolerance adherence checking
- **Straightness Analysis:** PCA-based axis deviation

---

## Anomaly Detection Framework

**Multi-Level Detection Strategy:**
- **Rule-based compliance** checking against IFC specifications
- **Statistical deviation analysis** for outlier detection
- **Multi-class anomaly flagging** system for different defect types

---

## Expected Output: Classification Visualization

**Proposed Visualization:**
- 3D point cloud with color-coded classifications
- Heatmap overlay showing pile detection confidence
- Geometric compliance indicators
- Deviation measurements from IFC specifications

*Note: Mockup visualization - actual results pending implementation*

---

## Experimental Design  
### Systematic Comparative Evaluation

**Evaluation Strategy:**
1. **Baseline Establishment:** Traditional methods performance benchmarking
2. **ML Method Implementation:** PointNet++ and DGCNN training and evaluation
3. **Head-to-Head Comparison:** Quantitative performance analysis
4. **Method Selection:** Data-driven choice of optimal approaches
5. **Pipeline Integration:** Best-performing methods combined into unified system

---

## Data Access & Sources

- **Real-World Data:** Sourced from active solar construction sites  
- **Data Types:**  
  - Drone-captured **point clouds** (LAS format)  
  - **IFC models** and CAD files for design reference  
  - Ground truth via **manual survey** and expert validation  
- **Access Status:**  
  - Data access secured via partner organizations (under NDA)  
  - Diverse site conditions covering multiple geographies and construction phases  
- **Usage Rights:**  
  - Academic use approved  
  - Data will not be published or shared externally  
**Scale:** 5+ sites, ~500M points per site, 1 cm GSD photogrammetry (as per SOP)
**Diversity:** Multiple terrain types, construction phases, weather conditions

---
## Labeling Strategy & Domain Adaptation

- IFC data enables automatic, geometry-driven labeling for one site  
- CAD sites lack IFC semantics; labeling aided by:  
  - Use simple rules to get labels from CAD geometry 
  - Start with models trained on IFC data and adapt them to CAD sites (transfer learning) 
  - Minimal manual annotation for calibration  
- EThis approach lets us apply deep learning models on many sites, even without IFC data

---

## Validation Framework

**Ground Truth Sources:**
- IFC design specifications and tolerances
- Manual survey measurements and expert validation
- Cross-site validation with varying construction conditions
**Statistical Rigor:** 5-fold cross-validation, paired t-tests (p<0.05)

**Performance Metrics:**
- Classification accuracy (Precision, Recall, F1-score)
- Geometric measurement accuracy
- Processing efficiency and scalability
- Robustness under different site conditions

---

## Target Performance Benchmarks

| Metric                    | Current Baseline | Research Target | Industry Best-Practice |
|---------------------------|------------------|-----------------|------------------------|
| Point Classification F1   | TBD             | 0.90+           | 0.95+                  |
| Verticality Error         | TBD             | < 2°            | < 1°                   |
| Spacing Accuracy          | TBD             | ±2 cm           | ±1 cm                  |
| Processing Time/Site      | TBD             | < 30 min        | < 15 min               |

*Baseline values to be established through traditional method implementation*

---

## Risk Mitigation Strategy

**Technical Risk Management:**
- **Data Quality Issues:** Multi-stage filtering and validation processes
- **Limited Training Data:** IFC-guided automatic labeling + manual validation
- **Method Selection Bias:** Systematic comparative evaluation with statistical testing
- **Computational Constraints:** Performance optimization and cloud processing options
- **Alignment Accuracy:** Multiple registration approaches with validation metrics
**Contingency Plans:** Plan B focuses on best-performing methods if compute-limited

---

## Implementation Timeline

| Phase | Focus Area | Timeline | Key Deliverables |
|-------|------------|----------|------------------|
| **1** | Method Implementation & Data Prep | May–June 2025 | Traditional baselines, data pipeline |
| **2** | ML Training & Comparative Analysis | June–July 2025 | Trained models, performance comparison |
| **3** | Integration & Geometric Analysis | Late July–Aug 10 | Unified pipeline, validation results |
| **4** | Documentation & Final Validation | Aug 11–Aug 17 | Final report, system demonstration |

---

## Expected Contributions

**Technical Innovation:**
- First comprehensive ML vs Traditional method comparison for solar foundation QA
- Validated method selection framework for construction point cloud processing
- IFC-integrated automated compliance validation pipeline
**Academic Publications:** Target ICCV/CVPR + Automation in Construction journal
**Open Source:** Benchmarking framework for research community

**Practical Impact:**
- Evidence-based recommendations for industry adoption
- Scalable QA solution with proven performance characteristics
- Reduced inspection time and improved accuracy in construction monitoring

---

## Academic Value

**Research Contributions:**
- Rigorous comparative methodology with statistical validation
- Open-source implementation for reproducible research
- Benchmarking framework applicable to other construction monitoring domains
- Cross-domain transfer of point cloud processing techniques

**Knowledge Gaps Addressed:**
- Lack of systematic comparison between ML and traditional methods in construction QA
- Limited validation of deep learning approaches on real construction site data
- Missing integration framework for CAD/IFC-based automated validation

---

## SOP Alignment with Dissertation

**SOP Source:** Datasee.AI Photogrammetric Construction Monitoring SOP  
**Purpose:** Ensure upstream drone data acquisition meets research requirements

### ✅ Aligned Aspects:
- **Flight Geometry:** Crosshatch + Nadir capture (supports spacing & verticality analysis)  
- **GSD (1 cm):** Matches geometric accuracy needs for pile-level QA  
- **GCP Benchmarks:** Enable ground-truth alignment for validation framework  
- **Camera Angle Requirements:** Support pile twist and spacing evaluations  
- **Data Upload:** Structured and version-controlled for reproducibility

---

## Gaps & Clarifications

### ⚠️ Minor Gaps:
- **ML Pipeline Not Mentioned in SOP:**  
  Clarified in research — ML is applied *after* data capture using SOP outputs.

- **GSD Awareness in Model Training:**  
  Addressed by scaling and normalization during model pre-processing.

- **Drone Hardware Assumptions:**  
  Dataset in use confirmed to follow SOP specs (48MP, DJI, overlaps).

### ✅ Integration Summary:
This research assumes **full SOP compliance** for upstream data, and builds a **downstream ML+IFC/CAD As-built verification system** aligned with that data quality standard.

---

## Questions & Discussion

**Key Discussion Points:**
- Method selection criteria and evaluation metrics
- Generalization across different solar construction sites
- Integration challenges with existing construction workflows
- Scalability and deployment considerations

Thank you for your attention!