# Quickstart Guide

This guide provides a quick introduction to using the Solar Panel Point Cloud Analyzer for analyzing solar panel installations using 3D point cloud data.

## Basic Workflow

The typical workflow for analyzing solar panel installations consists of the following steps:

1. **Load Point Cloud Data**: Load point cloud data from various file formats
2. **Align Point Clouds**: Align point clouds if working with multiple scans
3. **Detect Planes**: Detect planes representing solar panels
4. **Extract Features**: Extract geometric features from detected planes
5. **Detect Anomalies**: Identify anomalies in the solar panel installation
6. **Visualize Results**: Visualize the results for inspection

## Loading Point Cloud Data

```python
from solar_panel_analyzer.utils import data_loading

# Load point cloud from file
point_cloud = data_loading.load_point_cloud("path/to/point_cloud.obj")

# Load point cloud from IFC file
ifc_point_cloud = data_loading.load_from_ifc("path/to/building.ifc")

# Load point cloud from LAS file
las_point_cloud = data_loading.load_from_las("path/to/lidar.las")

# Preprocess point cloud
preprocessed_cloud = data_loading.preprocess_point_cloud(
    point_cloud,
    downsample=True,
    voxel_size=0.05,
    remove_outliers=True
)
```

## Aligning Point Clouds

```python
from solar_panel_analyzer import alignment

# Align using ICP
aligned_cloud, transformation_icp = alignment.icp.align_point_clouds(
    source_cloud,
    target_cloud,
    max_iterations=50,
    tolerance=1e-6
)

# Align using neural network
aligned_cloud, transformation_nn = alignment.neural_network.align_point_clouds(
    source_cloud,
    target_cloud
)

# Align using hybrid approach
aligned_cloud, transformation_hybrid = alignment.hybrid.align_point_clouds_hybrid(
    source_cloud,
    target_cloud
)
```

## Detecting Planes

```python
from solar_panel_analyzer import plane_detection

# Detect planes using RANSAC
planes = plane_detection.ransac.detect_planes(
    point_cloud,
    distance_threshold=0.01,
    num_iterations=1000,
    min_points=100
)

# Cluster points using DBSCAN
clusters = plane_detection.dbscan.cluster_points(
    point_cloud,
    eps=0.1,
    min_samples=10
)

# Extract planes from clusters
planes = plane_detection.extract_planes_from_clusters(clusters)
```

## Extracting Geometric Features

```python
from solar_panel_analyzer import geometric_analysis

# Calculate tilt angles
tilt_angles = geometric_analysis.tilt_analysis.calculate_tilt_angles(planes)

# Analyze spacing between panels
spacing = geometric_analysis.spacing_analysis.calculate_spacing(planes)

# Extract all geometric features
features = geometric_analysis.feature_extraction.extract_features(planes)
```

## Detecting Anomalies

```python
from solar_panel_analyzer import classification

# Load or train anomaly detection model
model = classification.models.load_model("path/to/model")

# Detect anomalies
anomalies = classification.anomaly_detection.detect_anomalies(
    features,
    model,
    threshold=0.8
)

# Get anomaly scores
anomaly_scores = classification.anomaly_detection.get_anomaly_scores(features, model)
```

## Visualizing Results

```python
from solar_panel_analyzer import visualization

# Visualize point cloud
visualization.point_cloud_vis.visualize_point_cloud(point_cloud)

# Visualize detected planes
visualization.point_cloud_vis.visualize_planes(point_cloud, planes)

# Visualize anomalies with heatmap
visualization.heatmap.visualize_anomaly_heatmap(
    point_cloud,
    planes,
    anomaly_scores
)

# Save visualization to file
visualization.save_visualization("path/to/output.html")
```

## Complete Example

Here's a complete example that demonstrates the entire workflow:

```python
import numpy as np
from solar_panel_analyzer import alignment, plane_detection, geometric_analysis, classification, visualization
from solar_panel_analyzer.utils import data_loading

# 1. Load point clouds
source_cloud = data_loading.load_point_cloud("data/sample/source.obj")
target_cloud = data_loading.load_point_cloud("data/sample/target.las")

# 2. Preprocess point clouds
source_cloud = data_loading.preprocess_point_cloud(
    source_cloud,
    downsample=True,
    voxel_size=0.05,
    remove_outliers=True
)
target_cloud = data_loading.preprocess_point_cloud(
    target_cloud,
    downsample=True,
    voxel_size=0.05,
    remove_outliers=True
)

# 3. Align point clouds
aligned_cloud, transformation = alignment.hybrid.align_point_clouds_hybrid(
    source_cloud,
    target_cloud
)

# 4. Detect planes
planes = plane_detection.ransac.detect_planes(
    aligned_cloud,
    distance_threshold=0.01,
    num_iterations=1000,
    min_points=100
)

# 5. Extract geometric features
features = geometric_analysis.feature_extraction.extract_features(planes)

# 6. Detect anomalies
anomaly_scores = classification.anomaly_detection.detect_anomalies(
    features,
    threshold=0.8
)

# 7. Visualize results
visualization.point_cloud_vis.visualize_planes(aligned_cloud, planes)
visualization.heatmap.visualize_anomaly_heatmap(
    aligned_cloud,
    planes,
    anomaly_scores
)

# 8. Save results
np.save("results/features.npy", features)
np.save("results/anomaly_scores.npy", anomaly_scores)
visualization.save_visualization("results/visualization.html")
```

## Next Steps

- Check out the [Example Scripts](../examples/) for more detailed examples
- Explore the [Tutorials](tutorials/) for step-by-step guides
- Read the [Technical Documentation](../technical/) for detailed information about each component
