# Geometric Analysis of Solar Panels

This guide explains how to use the geometric analysis notebook to extract and analyze geometric properties of solar panels detected in point cloud data.

## Introduction

After detecting planes using RANSAC and segmenting them into individual panels using DBSCAN, the next step is to analyze the geometric properties of these panels. This includes calculating tilt and azimuth angles, measuring panel dimensions, grouping panels into rows and columns, and calculating spacing between panels.

This guide will walk you through the process of using the geometric analysis implementation in the `notebooks/geometric_analysis/panel_geometry_analysis.ipynb` notebook.

## Prerequisites

Before using the geometric analysis notebook, you should have:

1. Run the RANSAC plane detection notebook (`notebooks/plane_detection/ransac_plane_detection.ipynb`)
2. Run the DBSCAN clustering notebook (`notebooks/plane_detection/dbscan_clustering.ipynb`)
3. Generated segmented panel data and saved it to the output directory
4. Python environment with the required dependencies:
   - NumPy
   - Matplotlib
   - Open3D (for 3D visualization)
   - pandas
   - scipy
   - scikit-learn

## Step-by-Step Guide

### 1. Prepare Your Data

The geometric analysis notebook expects to find the output from the DBSCAN clustering notebook. Make sure you have run the DBSCAN notebook and have the following files in your output directory:

- `segmented_panels.npy`: Information about the segmented panels
- `panel_1.ply`, `panel_2.ply`, etc.: Point clouds for each segmented panel

### 2. Configure the Notebook

Open the `notebooks/geometric_analysis/panel_geometry_analysis.ipynb` notebook and locate the section where you can specify the path to your data directory. You'll need to update:

- The `base_path` variable to point to your data directory
- The `output_dir` variable to point to the directory containing the DBSCAN output

These variables are found in the "Loading Segmented Panels" section of the notebook.

### 3. Run the Notebook

Execute the notebook cells sequentially to:

1. Load the segmented panels
2. Calculate geometric properties for each panel
3. Group panels into rows and columns
4. Calculate spacing between panels
5. Visualize the geometric properties
6. Perform statistical analysis
7. Save the results for further analysis

The notebook is designed to be run from start to finish, but you can also run individual sections to focus on specific aspects of the analysis.

### 4. Interpret the Results

The notebook will output a DataFrame containing various geometric properties for each panel, including:

- **Panel ID**: Unique identifier for each panel
- **Plane ID**: ID of the plane from which the panel was segmented
- **Cluster ID**: ID of the cluster within the plane
- **Centroid**: 3D coordinates of the panel center
- **Normal Vector**: Vector perpendicular to the panel surface
- **Length and Width**: Dimensions of the panel
- **Area**: Surface area of the panel
- **Tilt Angle**: Angle between the panel and the horizontal plane
- **Azimuth Angle**: Compass direction that the panel faces
- **Row and Column**: Position of the panel in the installation layout
- **Row Spacing**: Distance to the next row
- **Column Spacing**: Distance to the next panel in the same row

### 5. Visualize the Results

The notebook provides several visualization functions:

1. **Property Visualization**: 3D scatter plots with color coding based on properties like tilt, azimuth, or area. This helps identify patterns and anomalies in the panel installation.

2. **Normal Vector Visualization**: 3D quiver plots showing the orientation of each panel. This is useful for verifying that panels are oriented correctly.

3. **Row and Column Visualization**: 3D scatter plots with color coding based on row assignment. This helps understand the layout of the installation.

4. **Statistical Plots**: Histograms and scatter plots showing the distribution of key properties. These are useful for identifying outliers and understanding the overall characteristics of the installation.

### 6. Save the Results

The notebook saves the geometric analysis results in two formats:

1. **CSV File**: A comma-separated values file (`panel_geometry.csv`) that can be opened in Excel or other spreadsheet software.

2. **NumPy File**: A binary file (`panel_geometry.npy`) that can be loaded in other Python scripts for further analysis.

## Tips for Optimal Results

- **Preprocessing**: Ensure your point clouds are properly preprocessed (downsampled and cleaned) before applying geometric analysis.

- **Panel Grouping**: The row and column grouping is based on the spatial arrangement of panels. If your installation has an irregular layout, you may need to adjust the clustering parameters.

- **Visualization**: Use the visualization tools to verify that the geometric properties are calculated correctly. Look for anomalies in tilt, azimuth, or dimensions.

- **Statistical Analysis**: Use the statistical analysis to identify outliers and understand the overall characteristics of the installation.

## Next Steps

After analyzing the geometric properties of the panels, you can:

1. **Detect Anomalies**: Identify panels with unusual orientation or dimensions.

2. **Optimize Layout**: Analyze panel spacing to optimize land use and minimize shading.

3. **Predict Performance**: Use orientation data to predict solar energy production.

4. **Generate Reports**: Create reports for solar installation inspection.

## Troubleshooting

### Incorrect Dimensions

If panel dimensions seem incorrect, check:
- The point cloud density (too sparse may lead to inaccurate dimensions)
- The convex hull calculation (ensure it's capturing the panel boundary correctly)
- The units of measurement (ensure consistency throughout the pipeline)

### Incorrect Orientation

If panel orientation (tilt or azimuth) seems incorrect, check:
- The coordinate system (ensure the z-axis is vertical)
- The normal vector calculation (ensure it's pointing outward from the panel)
- The reference directions (ensure north is aligned with the y-axis)

### Row/Column Grouping Issues

If panels are not grouped into rows and columns correctly, try:
- Adjusting the `min_distance` parameter in the `cluster_panels_into_rows` method
- Verifying that the installation has a regular grid layout
- Manually assigning row and column indices for irregular layouts

## References

For more information on geometric analysis of solar panels, see:

1. The technical documentation: `docs/technical/geometric_analysis/overview.md`
2. Jochem, A., Höfle, B., Rutzinger, M., & Pfeifer, N. (2009). Automatic roof plane detection and analysis in airborne lidar point clouds for solar potential assessment. Sensors, 9(7), 5241-5262.
