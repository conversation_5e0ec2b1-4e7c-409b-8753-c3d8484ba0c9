# Solar Panel Segmentation with DBSCAN

This guide explains how to use DBSCAN clustering to segment detected planes into individual solar panels.

## Introduction

After detecting planes in a point cloud using RANSAC, the next step is to segment these planes into individual solar panels. This is where Density-Based Spatial Clustering of Applications with Noise (DBSCAN) comes in. DBSCAN can identify distinct clusters of points on a plane, which typically correspond to individual solar panels.

This guide will walk you through the process of using the DBSCAN implementation in the `notebooks/plane_detection/dbscan_clustering.ipynb` notebook.

## Prerequisites

Before using the DBSCAN clustering notebook, you should have:

1. Run the RANSAC plane detection notebook (`notebooks/plane_detection/ransac_plane_detection.ipynb`)
2. Generated plane point clouds and saved them to the output directory
3. Python environment with the required dependencies:
   - NumPy
   - Matplotlib
   - Open3D (for 3D visualization)
   - scikit-learn (for DBSCAN implementation)

## Step-by-Step Guide

### 1. Prepare Your Data

The DBSCAN notebook expects to find the output from the RANSAC plane detection notebook. Make sure you have run the RANSAC notebook and have the following files in your output directory:

- `detected_planes.npy`: Information about the detected planes
- `plane_1.ply`, `plane_2.ply`, etc.: Point clouds for each detected plane

### 2. Configure the Notebook

Open the `notebooks/plane_detection/dbscan_clustering.ipynb` notebook and locate the section where you can specify the path to your data directory. You'll need to update:

- The `base_path` variable to point to your data directory
- The `output_dir` variable to point to the directory containing the RANSAC output

These variables are found in the "Loading Detected Planes" section of the notebook.

### 3. Adjust DBSCAN Parameters

The DBSCAN algorithm has several parameters that can be adjusted to optimize clustering for your specific point cloud. The notebook includes automatic parameter estimation, but you may want to adjust:

- **normal_weight**: The weight given to normal vectors in the feature vector (default: 0.3)
- **use_normals**: Whether to use normal vectors for clustering (default: True)
- **min_samples**: The minimum number of points required to form a cluster (default: 1% of points)

These parameters are found in the "Apply DBSCAN to Each Plane" section of the notebook.

### 4. Run the Notebook

Execute the notebook cells sequentially to:

1. Load the planes detected by RANSAC
2. Apply DBSCAN clustering to each plane
3. Visualize the segmented panels
4. Calculate geometric properties for each panel
5. Save the results for further analysis

The notebook is designed to be run from start to finish, but you can also run individual sections to experiment with different parameters.

### 5. Interpret the Results

The notebook will output information about each identified cluster (potential solar panel), including:

- The number of points in the cluster
- The centroid (center point) of the cluster
- The normal vector (orientation) of the cluster
- The dimensions (length and width) of the cluster
- The area of the cluster
- The tilt angle (angle from horizontal) of the cluster
- The azimuth (compass direction) of the cluster

This information can be used to analyze the layout and orientation of the solar panels.

### 6. Visualize the Results

The notebook provides two visualization methods:

1. **Matplotlib Visualization**: This provides static plots of the segmented panels, with each panel shown in a different color. This is useful for documentation and reports.

2. **Open3D Visualization**: This provides an interactive 3D view of the segmented panels, allowing you to rotate and zoom to inspect the results. This is useful for detailed analysis.

### 7. Save the Results

The notebook saves the segmented panels and their properties in two formats:

1. **NumPy File**: A single file (`segmented_panels.npy`) containing information about all segmented panels, including their geometric properties.

2. **PLY Files**: Separate files for each segmented panel (`panel_1.ply`, `panel_2.ply`, etc.), which can be loaded into other 3D software for further analysis or visualization.

## Tips for Optimal Results

- **Parameter Tuning**: The automatic parameter estimation works well in most cases, but you may need to adjust parameters for specific datasets.

- **Feature Weighting**: The `normal_weight` parameter controls the balance between spatial coordinates and normal vectors. A higher value gives more importance to normal vectors, which can help separate panels with different orientations.

- **Minimum Cluster Size**: Adjust the `min_samples` parameter based on the density of your point cloud. A higher value will result in fewer, more robust clusters.

- **Visualization**: Use the visualization tools to verify that the segmentation corresponds to actual solar panels. If panels are merged or split incorrectly, adjust the parameters and try again.

## Next Steps

After segmenting planes into individual panels, you can:

1. **Analyze Panel Layout**: Study the arrangement and spacing of panels.
2. **Evaluate Panel Orientation**: Check if panels are oriented optimally for solar exposure.
3. **Detect Anomalies**: Identify panels with unusual orientation or dimensions.
4. **Extract Features**: Extract geometric features for machine learning-based anomaly detection.

## Troubleshooting

### Poor Segmentation Results

If panels are not segmented correctly (merged or split incorrectly), try:

- Adjusting the `eps` parameter (manually override the automatic estimation)
- Changing the `normal_weight` parameter
- Increasing or decreasing the `min_samples` parameter

### Missing Panels

If some panels are not detected, check:

- The RANSAC results to ensure all planes were detected
- The minimum cluster size to ensure small panels are not filtered out
- The noise threshold to ensure panels with sparse points are not classified as noise

### Slow Performance

If the notebook runs too slowly, try:

- Downsampling the point clouds further
- Processing only a subset of the planes
- Reducing the number of points used for parameter estimation

## References

For more information on the DBSCAN algorithm and its implementation, see:

1. The technical documentation: `docs/technical/plane_detection/dbscan.md`
2. Ester, M., Kriegel, H. P., Sander, J., & Xu, X. (1996). A density-based algorithm for discovering clusters in large spatial databases with noise. In Proceedings of the 2nd International Conference on Knowledge Discovery and Data Mining (KDD-96) (pp. 226-231).
