# Plane Detection with RANSAC

This guide explains how to use the RANSAC algorithm for detecting planes in point cloud data, particularly for solar panel installations.

## Introduction

Random Sample Consensus (RANSAC) is a powerful algorithm for detecting geometric shapes in point cloud data that contains noise and outliers. In the context of solar panel inspection, RANSAC can be used to detect the flat planes that represent individual solar panels.

This guide will walk you through the process of using the RANSAC implementation in the `notebooks/plane_detection/ransac_plane_detection.ipynb` notebook.

## Prerequisites

Before using the RANSAC plane detection notebook, you should have:

1. A preprocessed and aligned point cloud of a solar panel installation
2. Python environment with the required dependencies:
   - NumPy
   - Matplotlib
   - Open3D (for 3D visualization)
   - scikit-learn

## Step-by-Step Guide

### 1. Prepare Your Point Cloud

The first step is to prepare your point cloud data. The notebook expects an aligned point cloud, which can be generated using the alignment notebooks:

- `notebooks/alignment/icp_pc_alignment.ipynb`
- `notebooks/alignment/neural_network_pc_alignment.ipynb`
- `notebooks/alignment/point_cloud_alignment_benchmark_comparison.ipynb`

The aligned point cloud should be saved in PLY, PCD, or OBJ format.

### 2. Configure the Notebook

Open the `notebooks/plane_detection/ransac_plane_detection.ipynb` notebook and locate the section where you can specify the path to your aligned point cloud file. You'll need to update:

- The `base_path` variable to point to your data directory
- The `aligned_pc_path` variable to point to your aligned point cloud file

These variables are found in the "Load and Process Point Cloud" section of the notebook.

### 3. Adjust RANSAC Parameters

The RANSAC algorithm has several parameters that can be adjusted to optimize plane detection for your specific point cloud. These parameters are found in the "Detect Planes using RANSAC" section of the notebook:

- **distance_threshold**: The maximum distance for a point to be considered an inlier
- **num_iterations**: The number of RANSAC iterations to perform
- **min_inliers**: The minimum number of inliers required to accept a plane
- **max_planes**: The maximum number of planes to detect
- **min_ratio**: The minimum ratio of inliers to remaining points

Parameter tuning tips:
- Start with a small distance threshold (e.g., 0.01-0.05) and adjust based on results
- More iterations (1000+) provide better results but increase computation time
- Set min_inliers based on the expected size of the smallest plane
- Set max_planes based on how many distinct planes you expect in your point cloud

### 4. Run the Notebook

Execute the notebook cells sequentially to:

1. Load and preprocess the point cloud
2. Detect planes using RANSAC
3. Visualize the detected planes
4. Save the results for further analysis

The notebook is designed to be run from start to finish, but you can also run individual sections to experiment with different parameters.

### 5. Interpret the Results

The notebook will output information about each detected plane, including:

- The number of inlier points and their ratio to the total point cloud
- The plane equation in the form ax + by + cz + d = 0
- The normal vector of the plane [a, b, c]

The normal vector is particularly important for solar panel analysis as it indicates the orientation of the panel. For example, a normal vector of [0, 0, 1] indicates a panel that is perfectly horizontal.

### 6. Visualize the Results

The notebook provides two visualization methods:

1. **Open3D Visualization**: This provides an interactive 3D view of the point cloud with each detected plane highlighted in a different color. This is useful for exploring the data interactively.

2. **Matplotlib Visualization**: This provides static plots of each detected plane, which can be useful for reports and documentation. Each plane is shown in a separate subplot with inliers highlighted.

### 7. Save the Results

The notebook saves the detected planes in two formats:

1. **NumPy File**: A single file containing information about all detected planes, including their equations and inlier counts.
2. **PLY Files**: Separate files for each detected plane, which can be loaded into other 3D software for further analysis or visualization.

## Tips for Optimal Results

- **Preprocessing**: Ensure your point cloud is properly preprocessed (downsampled and cleaned) before applying RANSAC.
- **Parameter Tuning**: Experiment with different RANSAC parameters to find the optimal settings for your specific point cloud.
- **Distance Threshold**: This is the most critical parameter. Start with a small value and increase it if necessary.
- **Visualization**: Use the visualization tools to verify that the detected planes correspond to actual solar panels.
- **Iterative Approach**: Start with a small number of planes and gradually increase if needed.

## Next Steps

After detecting planes with RANSAC, you can:

1. **Analyze Plane Orientation**: Calculate tilt angles and azimuth angles for each detected plane.
2. **Measure Panel Dimensions**: Compute the dimensions and area of each detected panel.
3. **Detect Anomalies**: Compare the detected planes to identify anomalies such as misaligned panels.
4. **Extract Features**: Extract geometric features for machine learning-based anomaly detection.

## Troubleshooting

### No Planes Detected

If no planes are detected, try:
- Increasing the distance threshold parameter
- Decreasing the minimum inliers parameter
- Increasing the number of iterations

### Too Many Small Planes Detected

If too many small, insignificant planes are detected, try:
- Increasing the minimum inliers parameter
- Increasing the minimum ratio parameter
- Preprocessing the point cloud to remove noise

### Slow Performance

If the notebook runs too slowly, try:
- Reducing the number of iterations
- Further downsampling your point cloud
- Reducing the maximum number of planes to detect

## References

For more information on the RANSAC algorithm and its implementation, see:

1. The technical documentation: `docs/technical/plane_detection/ransac.md`
2. Fischler, M. A., & Bolles, R. C. (1981). Random sample consensus: a paradigm for model fitting with applications to image analysis and automated cartography. Communications of the ACM, 24(6), 381-395.
3. Schnabel, R., Wahl, R., & Klein, R. (2007). Efficient RANSAC for point-cloud shape detection. Computer Graphics Forum, 26(2), 214-226.
