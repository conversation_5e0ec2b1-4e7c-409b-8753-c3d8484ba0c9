# 🚀 Getting Started

Welcome to the drone photogrammetry analysis workflow! This section provides everything you need to get up and running quickly.

## 📋 Quick Navigation

### 🔧 **Setup & Installation**
- [Installation Guide](installation.md) - Complete setup instructions
- [Environment Setup](environment_setup.md) - Python environment and dependencies
- [Data Requirements](data_requirements.md) - Input data formats and requirements

### 🎯 **First Steps**
- [Quick Start Guide](quickstart.md) - 15-minute workflow overview
- [Basic Tutorial](basic_tutorial.md) - Your first complete analysis
- [Sample Data](sample_data.md) - Download and test with example datasets

### 📖 **Understanding the Workflow**
- [Workflow Overview](workflow_overview.md) - Complete pipeline explanation
- [Key Concepts](key_concepts.md) - Important terminology and concepts
- [Common Use Cases](use_cases.md) - Typical analysis scenarios

## 🎯 **Recommended Learning Path**

### For New Users:
1. **Start Here**: [Installation Guide](installation.md)
2. **Quick Demo**: [Quick Start Guide](quickstart.md)
3. **Learn Basics**: [Basic Tutorial](basic_tutorial.md)
4. **Understand Pipeline**: [Workflow Overview](workflow_overview.md)

### For Experienced Users:
1. **Setup**: [Environment Setup](environment_setup.md)
2. **Jump In**: [Advanced Tutorials](../02_technical/)
3. **Customize**: [Configuration Guide](configuration.md)

## 🔗 **What's Next?**

After completing the getting started section:

- **Technical Details**: Explore [Technical Documentation](../02_technical/) for in-depth guides
- **Research Background**: Check [Research Documentation](../03_research/) for methodology
- **Contributing**: See [Development Documentation](../04_development/) to contribute

## 🆘 **Need Help?**

- **Common Issues**: [Troubleshooting Guide](troubleshooting.md)
- **FAQ**: [Frequently Asked Questions](faq.md)
- **Support**: [Getting Help](getting_help.md)

---

**💡 Tip**: Start with the [Quick Start Guide](quickstart.md) if you want to see results immediately, or follow the [Installation Guide](installation.md) for a complete setup.
