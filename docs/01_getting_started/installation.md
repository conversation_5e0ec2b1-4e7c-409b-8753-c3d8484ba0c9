# Installation Guide

This guide provides instructions for installing the Solar Panel Point Cloud Analyzer.

## Prerequisites

Before installing the Solar Panel Point Cloud Analyzer, ensure you have the following prerequisites:

- Python 3.8 or higher
- pip (Python package installer)
- Git (for cloning the repository)

### Optional Dependencies

- CUDA-compatible GPU (for accelerated neural network training)
- CUDA Toolkit 11.0 or higher (for GPU acceleration)

## Installation Methods

There are two ways to install the Solar Panel Point Cloud Analyzer:

1. **Standard Installation**: Install the latest release version
2. **Development Installation**: Install from source for development

## Standard Installation

To install the latest release version:

```bash
pip install solar-panel-analyzer
```

This will install the package and all its dependencies.

## Development Installation

For development or to access the latest features, install from source:

```bash
# Clone the repository
git clone https://github.com/username/solar-panel-point-cloud-analyzer.git
cd solar-panel-point-cloud-analyzer

# Install dependencies
pip install -r requirements.txt

# Install the package in development mode
pip install -e .
```

Installing in development mode (`-e`) allows you to modify the source code and have the changes immediately available without reinstalling.

## Dependencies

The Solar Panel Point Cloud Analyzer depends on the following Python packages:

- **NumPy**: For numerical operations
- **SciPy**: For scientific computing
- **scikit-learn**: For machine learning algorithms
- **TensorFlow** or **PyTorch**: For neural network implementation
- **Open3D**: For point cloud processing
- **Matplotlib**: For 2D visualization
- **Plotly**: For interactive 3D visualization
- **Pandas**: For data manipulation
- **IfcOpenShell**: For IFC file processing (optional)
- **laspy**: For LAS file processing (optional)
- **ezdxf**: For DWG/DXF file processing (optional)

These dependencies will be automatically installed when you install the package.

## GPU Support

To enable GPU acceleration for neural network training:

### For TensorFlow:

```bash
pip install tensorflow-gpu
```

### For PyTorch:

```bash
pip install torch torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cu116
```

Replace `cu116` with your CUDA version (e.g., `cu117` for CUDA 11.7).

## Verifying Installation

To verify that the installation was successful, run:

```bash
python -c "import solar_panel_analyzer; print(solar_panel_analyzer.__version__)"
```

This should print the version number of the installed package.

## Troubleshooting

### Common Issues

#### Missing Dependencies

If you encounter errors about missing dependencies, try installing them manually:

```bash
pip install <package_name>
```

#### CUDA Issues

If you're having issues with GPU acceleration:

1. Verify that your GPU is CUDA-compatible
2. Check that you have the correct CUDA Toolkit installed
3. Ensure that your GPU drivers are up to date

#### Import Errors

If you encounter import errors:

1. Check that you're using the correct Python environment
2. Verify that the package is installed (`pip list | grep solar-panel-analyzer`)
3. Try reinstalling the package

### Getting Help

If you continue to experience issues:

1. Check the [Troubleshooting Guide](../resources/troubleshooting.md) for more detailed solutions
2. Open an issue on the [GitHub repository](https://github.com/username/solar-panel-point-cloud-analyzer/issues)
3. Contact the maintainers for support

## Next Steps

After installation, you can:

- Follow the [Quickstart Guide](quickstart.md) to get started with the package
- Explore the [Example Scripts](../examples/) to see how to use the package
- Check out the [Tutorials](tutorials/) for step-by-step guides
