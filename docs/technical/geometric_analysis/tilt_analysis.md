# Tilt Angle Calculation

*This document is a placeholder for future documentation on tilt angle calculation for solar panels.*

## Overview

Tilt angle is a critical parameter for solar panel installations, affecting energy production efficiency. This document outlines the methods for calculating tilt angles from detected planes in point cloud data.

## Planned Implementation

### Mathematical Foundation

The tilt angle of a plane can be calculated from its normal vector:

1. For a plane with normal vector n = (nx, ny, nz), the tilt angle θ relative to the horizontal plane is:
   
   θ = arccos(nz / |n|)
   
   where |n| is the magnitude of the normal vector.

2. For solar panels, we may also want to calculate the azimuth angle φ (orientation):
   
   φ = arctan2(ny, nx)

### Implementation Steps

1. Extract the normal vector from the plane equation (Ax + By + Cz + D = 0)
2. Normalize the vector to unit length
3. Calculate the tilt angle using the formula above
4. Validate the angle against expected ranges for solar panels

### Handling Special Cases

- Vertical panels (θ ≈ 90°)
- Horizontal panels (θ ≈ 0°)
- Noisy or imperfect plane fits

## Integration with Other Components

- Input: Detected and clustered planes from RANSAC and DBSCAN
- Output: Tilt angle measurements for each detected plane
- Next step: Feature vector creation for ML classification

## Evaluation Metrics

- Accuracy compared to ground truth measurements
- Consistency across similar panels
- Robustness to noise and outliers

## References

1. <PERSON>, <PERSON>, & <PERSON>, A. (2003). Multiple view geometry in computer vision. Cambridge University Press.

2. Rabbani, T., van den Heuvel, F., & Vosselman, G. (2006). Segmentation of point clouds using smoothness constraint. International Archives of Photogrammetry, Remote Sensing and Spatial Information Sciences, 36(5), 248-253.

## TODO

- [ ] Implement normal vector extraction from plane equations
- [ ] Develop tilt angle calculation function
- [ ] Create validation methods for angle measurements
- [ ] Integrate with plane detection pipeline
- [ ] Visualize tilt angles on 3D point cloud
