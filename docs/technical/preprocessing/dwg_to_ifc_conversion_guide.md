# DWG to IFC Conversion Guide Using FreeCAD

This guide provides detailed instructions for converting DWG (AutoCAD) files to IFC (Industry Foundation Classes) format using FreeCAD, a free and open-source CAD application.

## Prerequisites

- Computer with Windows, macOS, or Linux
- Internet connection for downloading software
- Administrative privileges for software installation
- Basic familiarity with CAD concepts

## Step 1: Install FreeCAD

### Windows
1. Download the latest stable version from the [official FreeCAD website](https://www.freecadweb.org/downloads.php)
2. Run the installer and follow the prompts
3. Complete the installation

### macOS
1. Download the macOS version from the [official FreeCAD website](https://www.freecadweb.org/downloads.php)
2. Open the downloaded .dmg file
3. Drag FreeCAD to your Applications folder

### Linux
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install freecad

# Fedora
sudo dnf install freecad

# Arch Linux
sudo pacman -S freecad
```

## Step 2: Install the ODA File Converter

FreeCAD needs this tool to properly handle DWG files.

1. Go to the [ODA File Converter download page](https://www.opendesign.com/guestfiles/oda_file_converter)
2. Select the version for your operating system:
   - Windows: ODAFileConverter_QT5_x64.exe
   - macOS: ODAFileConverter_QT5.dmg
   - Linux: ODAFileConverter_QT5_lnxX64.deb

3. Download and install the converter
   - Windows: Run the installer and follow the prompts
   - macOS: Open the .dmg and follow installation instructions
   - Linux: `sudo dpkg -i ODAFileConverter_QT5_lnxX64.deb` (or use your package manager)

## Step 3: Configure FreeCAD to Use the ODA File Converter

1. Open FreeCAD
2. Go to **Edit → Preferences → Import-Export → DWG**
3. Under **Path to Teigha File Converter**, set the path to the ODAFileConverter executable:
   - Windows: Typically `C:\Program Files\ODA\ODAFileConverter\ODAFileConverter.exe`
   - macOS: Typically `/Applications/ODAFileConverter.app/Contents/MacOS/ODAFileConverter`
   - Linux: Typically `/usr/bin/ODAFileConverter`
4. Click **OK** to save preferences
5. Restart FreeCAD

## Step 4: Import Your DWG File

1. Open FreeCAD
2. Go to **File → Import**
3. Select your DWG file
4. Wait for the import to complete (this may take some time for complex files)

## Step 5: Prepare Your Model for IFC Export

For optimal results in IFC format:

1. Switch to the **Arch Workbench** (View → Workbench → Arch)
2. Select 2D elements that should be walls and use the **Arch Wall** tool
3. Select closed profiles and use the **Arch Structure** tool for structural elements
4. Group related elements (select multiple objects, right-click → Create Group)
5. Add a **Building** object:
   - Arch → Building
   - Set properties like Name, Address, etc.
6. Create **Storeys** within the building:
   - Arch → Floor
   - Drag and drop elements into appropriate floors

## Step 6: Export to IFC Format

1. Go to **File → Export**
2. Select **Industry Foundation Classes (*.ifc)** from the file type dropdown
3. In the **IFC export** dialog that appears, configure these settings:
   
   **Export options:**
   - **Export type**: Select "Standard model" from the dropdown
   - ✅ Store IFC unique ID in FreeCAD objects
   - ✅ Export 2D objects as IfcAnnotations
   - ✅ Reuse similar entities
   - ✅ Export nested groups as assemblies

   **IFC file units:**
   - Select "Metric" from the dropdown

   **IFC standard compliance:**
   - ✅ Add default building if one is not found in the document
   - ✅ Export nested groups as assemblies

4. Click **OK** to start the export process
5. Choose a location to save your IFC file
6. Wait for the export to complete

## Troubleshooting

### If DWG Import Fails:
1. Try using the DXF format as an intermediate step
   - Use the ODA File Converter to convert DWG to DXF first
   - Then import the DXF file into FreeCAD

### If IFC Export Shows Errors:
1. Check if your model has valid 3D geometry
2. Try simplifying the model before export
3. Make sure building elements are properly classified

### If Model Appears Empty or Incomplete:
1. Try changing import settings to include all layers
2. Check if the scale is correct (objects might be too small/large)
3. Try using the "Draft" workbench to manipulate 2D elements

## Advanced Tips for Complex Engineering Drawings

For complex drawings like ramming layouts:

1. Use the **Structure** tool in the Arch workbench to define structural elements
2. Group related elements before export
3. Set proper building storeys if your model has multiple levels
4. Consider adding IFC properties to elements for better BIM compatibility:
   - Select an object
   - Go to Properties panel → IFC Properties
   - Add relevant properties based on the element type

## Verifying Your IFC File

After export, you can verify your IFC file using:

1. **FreeCAD**: Re-import the IFC file to check for data integrity
2. **BIMvision**: Free IFC viewer available at [bimvision.eu](https://bimvision.eu/download/)
3. **Solibri Anywhere**: Free IFC viewer with validation tools

## Additional Resources

- [FreeCAD BIM Workbench Documentation](https://wiki.freecadweb.org/BIM_Workbench)
- [buildingSMART IFC Documentation](https://technical.buildingsmart.org/standards/ifc/)
- [OpeningDesign: FreeCAD & IFC Tutorial](https://www.youtube.com/c/OpeningDesign)