# Point Cloud Alignment Benchmark Source Code

This directory contains the source code for the Point Cloud Alignment Benchmark repository, which implements GeoPoseNet and provides tools for benchmarking different point cloud alignment methods.

## Structure

- `geoposenet/`: Main package implementing the GeoPoseNet algorithm
  - `models/`: Neural network models
    - `pose_regression_net.py`: Main pose regression network
  - `datasets/`: Data loading and processing
    - `data_loader.py`: Functions for loading and processing point clouds
  - `utils/`: Utility functions
    - `common.py`: Common utility functions
    - `icp_utils.py`: ICP refinement utilities
    - `logger.py`: Logging utilities
    - `tensorboard_logger.py`: TensorBoard logging utilities

## Usage

The code in this directory is meant to be imported by the scripts in the root directory and the benchmark notebook.
