# Point Cloud Alignment Benchmark

Benchmarking traditional and deep learning approaches for 3D point cloud alignment using real-world data.

---

## Overview

This repository explores and benchmarks various techniques for 3D point cloud alignment, including:

* Iterative Closest Point (ICP)
* Neural Network-based models (GeoPoseNet)
* Hybrid methods combining DL and ICP

The goal is to evaluate the effectiveness, accuracy, and robustness of each method on real-world datasets.

---

## Methods Compared

| Method        | Description                                      |
| ------------- | ------------------------------------------------ |
| ICP           | Traditional geometric alignment                  |
| Deep Learning | Neural network trained to predict transformation |
| Hybrid        | Neural network + ICP refinement                  |

---

## Results Summary

| Metric     | ICP          | Deep Learning | Hybrid       |
| ---------- | ------------ | ------------- | ------------ |
| Accuracy   | XX%          | XX%           | XX%          |
| Runtime    | XXs          | XXs           | XXs          |
| Robustness | High/Med/Low | High/Med/Low  | High/Med/Low |

(Details in the benchmark notebook: `examples/geoposenet_benchmark_example.ipynb`)

---

## Benchmark Methodology

The benchmark evaluates each method on:

1. **Accuracy**: How closely the aligned point cloud matches the target
2. **Speed**: Computational time required for alignment
3. **Robustness**: Performance under varying degrees of initial misalignment

Each method is tested on multiple datasets with different characteristics:
- Dense vs. sparse point clouds
- Complete vs. partial overlap
- Various degrees of initial misalignment

---

## Running the Benchmark

To run the benchmark yourself:

```bash
# Install dependencies
pip install -r requirements.txt

# Run the benchmark notebook
jupyter notebook examples/geoposenet_benchmark_example.ipynb
```

Alternatively, you can use the evaluation script for specific point clouds:

```bash
python evaluate.py --model_path saved_models/geoposenet_model.keras --source_path path/to/source.obj --target_path path/to/target.las
```

---

## Citation

If you find this benchmark useful, please consider citing:

```
@misc{pcalignment2024,
  title={Point Cloud Alignment Benchmark: ICP, Deep Learning and Hybrid Methods},
  author={Preetam Balijepalli},
  year={2024},
  url={https://github.com/balijepalli/pc-alignment-benchmark}
}
```
