#!/bin/bash

# =============================================================================
# Data Download Validation Script
# =============================================================================
# Validates downloaded project data and generates summary reports
# =============================================================================

set -euo pipefail

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*"; }

# Configuration
DATA_DIR="../data"
REPORT_FILE="validation_report_$(date +%Y%m%d_%H%M%S).txt"

validate_project_data() {
    local project_type=$1
    local project_name=$2
    local project_dir="$DATA_DIR/$project_type/$project_name"
    
    log_info "Validating $project_type/$project_name..."
    
    local issues=()
    local files_found=0
    
    # Check if project directory exists
    if [[ ! -d "$project_dir" ]]; then
        issues+=("Project directory missing: $project_dir")
        return 1
    fi
    
    # Check point cloud files
    local pc_files=$(find "$project_dir/pointcloud" -name "*.las" -o -name "*.laz" -o -name "*.pcd" 2>/dev/null | wc -l)
    if [[ $pc_files -eq 0 ]]; then
        issues+=("No point cloud files found")
    else
        files_found=$((files_found + pc_files))
        log_info "  Point cloud files: $pc_files"
    fi
    
    # Check CAD files
    local cad_files=$(find "$project_dir/cad" -name "*.dwg" -o -name "*.dxf" -o -name "*.ifc" 2>/dev/null | wc -l)
    if [[ $cad_files -eq 0 ]]; then
        issues+=("No CAD files found")
    else
        files_found=$((files_found + cad_files))
        log_info "  CAD files: $cad_files"
    fi
    
    # Check ortho files
    local ortho_files=$(find "$project_dir/ortho" -name "*.tif" -o -name "*.tiff" -o -name "*.jpg" 2>/dev/null | wc -l)
    if [[ $ortho_files -eq 0 ]]; then
        log_warn "  No ortho files found (may be expected for some projects)"
    else
        files_found=$((files_found + ortho_files))
        log_info "  Ortho files: $ortho_files"
    fi
    
    # Calculate total size
    local total_size=$(du -sh "$project_dir" 2>/dev/null | cut -f1)
    log_info "  Total size: $total_size"
    
    # Report issues
    if [[ ${#issues[@]} -gt 0 ]]; then
        log_error "  Issues found:"
        for issue in "${issues[@]}"; do
            log_error "    - $issue"
        done
        return 1
    else
        log_info "  ✅ Validation passed ($files_found files)"
        return 0
    fi
}

generate_summary_report() {
    log_info "Generating validation summary report..."
    
    {
        echo "Energy Inspection 3D - Data Download Validation Report"
        echo "====================================================="
        echo "Generated: $(date)"
        echo ""
        
        echo "ENEL Projects:"
        echo "-------------"
        for project in Castro Mudjar Giorgio; do
            echo "Project: $project"
            if [[ -d "$DATA_DIR/ENEL/$project" ]]; then
                echo "  Status: Downloaded"
                echo "  Size: $(du -sh "$DATA_DIR/ENEL/$project" 2>/dev/null | cut -f1)"
                echo "  Files: $(find "$DATA_DIR/ENEL/$project" -type f 2>/dev/null | wc -l)"
            else
                echo "  Status: Missing"
            fi
            echo ""
        done
        
        echo "USA Projects:"
        echo "------------"
        for project in McCarthy RPCS RES; do
            echo "Project: $project"
            if [[ -d "$DATA_DIR/USA/$project" ]]; then
                echo "  Status: Downloaded"
                echo "  Size: $(du -sh "$DATA_DIR/USA/$project" 2>/dev/null | cut -f1)"
                echo "  Files: $(find "$DATA_DIR/USA/$project" -type f 2>/dev/null | wc -l)"
            else
                echo "  Status: Missing"
            fi
            echo ""
        done
        
        echo "Overall Summary:"
        echo "---------------"
        if [[ -d "$DATA_DIR" ]]; then
            echo "Total data size: $(du -sh "$DATA_DIR" 2>/dev/null | cut -f1)"
            echo "Total files: $(find "$DATA_DIR" -type f 2>/dev/null | wc -l)"
        else
            echo "Data directory not found: $DATA_DIR"
        fi
        
        echo ""
        echo "File Types:"
        echo "----------"
        echo "Point clouds (.las/.laz): $(find "$DATA_DIR" -name "*.las" -o -name "*.laz" 2>/dev/null | wc -l)"
        echo "CAD files (.dwg/.dxf): $(find "$DATA_DIR" -name "*.dwg" -o -name "*.dxf" 2>/dev/null | wc -l)"
        echo "Ortho images (.tif/.jpg): $(find "$DATA_DIR" -name "*.tif" -o -name "*.tiff" -o -name "*.jpg" 2>/dev/null | wc -l)"
        
    } > "$REPORT_FILE"
    
    log_info "📋 Validation report saved: $REPORT_FILE"
}

main() {
    log_info "🔍 Starting data validation..."
    
    local failed_projects=()
    
    # Validate ENEL projects
    log_info "Validating ENEL projects..."
    for project in Castro Mudjar Giorgio; do
        if ! validate_project_data "ENEL" "$project"; then
            failed_projects+=("ENEL/$project")
        fi
    done
    
    # Validate USA projects
    log_info "Validating USA projects..."
    for project in McCarthy RPCS RES; do
        if ! validate_project_data "USA" "$project"; then
            failed_projects+=("USA/$project")
        fi
    done
    
    # Generate summary report
    generate_summary_report
    
    # Final summary
    if [[ ${#failed_projects[@]} -eq 0 ]]; then
        log_info "🎉 All project validations passed!"
    else
        log_error "❌ Failed validations: ${failed_projects[*]}"
        exit 1
    fi
}

# Show usage if requested
if [[ "${1:-}" == "--help" || "${1:-}" == "-h" ]]; then
    cat << EOF
Usage: $0 [project]

Validates downloaded project data.

Options:
  --help, -h    Show this help message

Examples:
  $0                    # Validate all projects
  $0 Castro            # Validate specific project
  $0 ENEL/Castro       # Validate specific project with type

EOF
    exit 0
fi

# Handle specific project validation
if [[ $# -eq 1 ]]; then
    project_arg="$1"
    if [[ "$project_arg" =~ ^(ENEL|USA)/(.+)$ ]]; then
        project_type="${BASH_REMATCH[1]}"
        project_name="${BASH_REMATCH[2]}"
    else
        # Try to determine project type
        case "$project_arg" in
            Castro|Mudjar|Giorgio)
                project_type="ENEL"
                project_name="$project_arg"
                ;;
            McCarthy|RPCS|RES)
                project_type="USA"
                project_name="$project_arg"
                ;;
            *)
                log_error "Unknown project: $project_arg"
                exit 1
                ;;
        esac
    fi
    
    validate_project_data "$project_type" "$project_name"
    exit $?
fi

# Run full validation
main
