{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Neural Network Point Cloud Alignment\n", "\n", "This notebook implements a deep learning approach for point cloud alignment using neural networks. It includes:\n", "1. A PointNet-like architecture for feature extraction\n", "2. Direct regression of transformation parameters (rotation and translation)\n", "3. A hybrid approach that combines neural networks with ICP for refinement\n", "\n", "We evaluate these methods on various metrics including accuracy, computational efficiency, and robustness to initialization.\n", "\n", "**Author:** <PERSON><PERSON><PERSON>  \n", "**Date:** June 2024"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Installation\n", "\n", "First, let's install the necessary dependencies and import required libraries."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "# Uncomment and run this cell if you need to install the packages\n", "\n", "# !pip install --upgrade --force-reinstall --ignore-installed blinker\n", "# !pip install numpy matplotlib tensorflow open3d scikit-learn transforms3d pandas"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import os\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import tensorflow as tf\n", "import transforms3d.euler as t3d_euler\n", "import transforms3d.quaternions as t3d_quaternions\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.neighbors import NearestNeighbors\n", "import pandas as pd\n", "import time\n", "import logging\n", "import sys\n", "\n", "# Add the src directory to the path so we can import our utilities\n", "# This assumes the notebook is in notebooks/alignment/ and the utilities are in src/utils/\n", "sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..', '..')))\n", "\n", "# Try to import our point cloud I/O utilities\n", "try:\n", "    from src.utils.point_cloud_io import save_point_cloud\n", "    POINT_CLOUD_IO_SUPPORT = True\n", "    print(\"Point cloud I/O utilities loaded successfully.\")\n", "except ImportError:\n", "    print(\"Warning: point_cloud_io module not found. Using built-in functions.\")\n", "    POINT_CLOUD_IO_SUPPORT = False\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, \n", "                    format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Try to import Open3D for point cloud visualization\n", "try:\n", "    import open3d as o3d\n", "    O3D_SUPPORT = True\n", "    logger.info(\"Open3D support is available.\")\n", "except ImportError:\n", "    logger.warning(\"open3d not installed. Some visualizations may not be available.\")\n", "    O3D_SUPPORT = False\n", "\n", "# Try to import laspy for LAS files\n", "try:\n", "    import laspy\n", "    LAS_SUPPORT = True\n", "    logger.info(\"LAS support is available.\")\n", "except ImportError:\n", "    logger.warning(\"laspy not installed. LAS files will not be supported.\")\n", "    LAS_SUPPORT = False\n", "\n", "# Try to import ifcopenshell for IFC files\n", "try:\n", "    import ifcopenshell\n", "    import ifcopenshell.geom\n", "    IFC_SUPPORT = True\n", "    logger.info(\"IFC support is available.\")\n", "except ImportError:\n", "    logger.warning(\"ifcopenshell not installed. IFC files will not be supported.\")\n", "    IFC_SUPPORT = False\n", "\n", "# Mount Google Drive if in Colab\n", "try:\n", "    from google.colab import drive\n", "    drive.mount('/content/gdrive')\n", "    IN_COLAB = True\n", "    logger.info(\"Google Drive mounted successfully.\")\n", "except ImportError:\n", "    IN_COLAB = False\n", "    logger.info(\"Not running in Google Colab. Using local file system.\")\n", "\n", "# Set random seeds for reproducibility\n", "np.random.seed(42)\n", "tf.random.set_seed(42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Neural Network Architecture\n", "\n", "Deep learning approaches have shown promising results for point cloud alignment. Here, we'll implement a neural network that directly regresses the transformation parameters (rotation and translation) from the input point clouds.\n", "\n", "Our neural network architecture consists of:\n", "1. PointNet-like feature extraction for both source and target point clouds\n", "2. Feature concatenation and fusion\n", "3. Regression heads for rotation (as quaternion) and translation parameters\n", "\n", "This approach has several advantages over traditional ICP:\n", "- Faster inference time once trained\n", "- More robust to noise and outliers\n", "- Better handling of partial overlaps\n", "- No need for good initialization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_point_cloud_alignment_model(num_points=1024, use_quaternion=True):\n", "    \"\"\"\n", "    Creates a neural network model for point cloud alignment.\n", "    \n", "    Parameters:\n", "    -----------\n", "    num_points : int\n", "        Number of points in each point cloud\n", "    use_quaternion : bool\n", "        Whether to use quaternion (True) or Euler angles (False) for rotation\n", "    \n", "    Returns:\n", "    --------\n", "    model : tf.keras.Model\n", "        TensorFlow model for point cloud alignment\n", "    \"\"\"\n", "    # Input layers\n", "    source_input = tf.keras.layers.Input(shape=(num_points, 3), name='source_input')\n", "    target_input = tf.keras.layers.Input(shape=(num_points, 3), name='target_input')\n", "    \n", "    # Feature extraction function (shared weights)\n", "    def point_feature_extraction(point_input, name_prefix):\n", "        # MLP layers for feature extraction\n", "        x = tf.keras.layers.Conv1D(64, 1, activation='relu', name=f'{name_prefix}_conv1')(point_input)\n", "        x = tf.keras.layers.BatchNormalization(name=f'{name_prefix}_bn1')(x)\n", "        x = tf.keras.layers.Conv1D(128, 1, activation='relu', name=f'{name_prefix}_conv2')(x)\n", "        x = tf.keras.layers.BatchNormalization(name=f'{name_prefix}_bn2')(x)\n", "        x = tf.keras.layers.Conv1D(256, 1, activation='relu', name=f'{name_prefix}_conv3')(x)\n", "        x = tf.keras.layers.BatchNormalization(name=f'{name_prefix}_bn3')(x)\n", "        \n", "        # Global feature extraction (max pooling)\n", "        global_features = tf.keras.layers.GlobalMaxPooling1D(name=f'{name_prefix}_global_max_pool')(x)\n", "        return global_features\n", "    \n", "    # Extract features from source and target point clouds\n", "    source_features = point_feature_extraction(source_input, 'source')\n", "    target_features = point_feature_extraction(target_input, 'target')\n", "    \n", "    # Concatenate features\n", "    combined_features = tf.keras.layers.Concatenate(name='combined_features')([source_features, target_features])\n", "    \n", "    # Fusion network\n", "    fusion = tf.keras.layers.Dense(512, activation='relu', name='fusion_fc1')(combined_features)\n", "    fusion = tf.keras.layers.BatchNormalization(name='fusion_bn1')(fusion)\n", "    fusion = tf.keras.layers.Dense(256, activation='relu', name='fusion_fc2')(fusion)\n", "    fusion = tf.keras.layers.BatchNormalization(name='fusion_bn2')(fusion)\n", "    fusion = tf.keras.layers.Dense(128, activation='relu', name='fusion_fc3')(fusion)\n", "    \n", "    # Rotation head\n", "    if use_quaternion:\n", "        # Quaternion representation (4 parameters)\n", "        rotation = tf.keras.layers.Dense(4, name='rotation_quaternion')(fusion)\n", "        # Normalize quaternion\n", "        rotation = tf.keras.layers.Lambda(\n", "            lambda x: x / tf.norm(x, axis=-1, keepdims=True),\n", "            name='normalized_quaternion'\n", "        )(rotation)\n", "    else:\n", "        # Euler angles representation (3 parameters)\n", "        rotation = tf.keras.layers.Dense(3, name='rotation_euler')(fusion)\n", "    \n", "    # Translation head\n", "    translation = tf.keras.layers.Dense(3, name='translation')(fusion)\n", "    \n", "    # Create model\n", "    model = tf.keras.Model(\n", "        inputs=[source_input, target_input],\n", "        outputs=[rotation, translation],\n", "        name='point_cloud_alignment_model'\n", "    )\n", "    \n", "    return model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Preparation and Training\n", "\n", "Before implementing the alignment algorithms, we need to prepare training data and train our neural network model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_random_transformation(rotation_range=np.pi/4, translation_range=0.5):\n", "    \"\"\"\n", "    Generates a random transformation (rotation and translation).\n", "    \n", "    Parameters:\n", "    -----------\n", "    rotation_range : float\n", "        Maximum rotation angle in radians\n", "    translation_range : float\n", "        Maximum translation distance\n", "    \n", "    Returns:\n", "    --------\n", "    R : numpy.n<PERSON><PERSON>\n", "        3x3 rotation matrix\n", "    t : numpy.n<PERSON><PERSON>\n", "        3x1 translation vector\n", "    euler_angles : numpy.n<PERSON><PERSON>\n", "        Euler angles (roll, pitch, yaw)\n", "    quaternion : numpy.n<PERSON><PERSON>\n", "        Quaternion representation of rotation\n", "    \"\"\"\n", "    # Generate random Euler angles\n", "    roll = np.random.uniform(-rotation_range, rotation_range)\n", "    pitch = np.random.uniform(-rotation_range, rotation_range)\n", "    yaw = np.random.uniform(-rotation_range, rotation_range)\n", "    euler_angles = np.array([roll, pitch, yaw])\n", "    \n", "    # Convert Euler angles to rotation matrix\n", "    R = t3d_euler.euler2mat(roll, pitch, yaw)\n", "    \n", "    # Convert to quaternion\n", "    quaternion = t3d_euler.euler2quat(roll, pitch, yaw)\n", "    \n", "    # Generate random translation\n", "    t = np.random.uniform(-translation_range, translation_range, size=3)\n", "    \n", "    return R, t, euler_angles, quaternion\n", "\n", "def apply_transformation(points, R, t):\n", "    \"\"\"\n", "    Applies a transformation (rotation and translation) to a point cloud.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud of shape (N, 3)\n", "    R : numpy.n<PERSON><PERSON>\n", "        3x3 rotation matrix\n", "    t : numpy.n<PERSON><PERSON>\n", "        3x1 translation vector\n", "    \n", "    Returns:\n", "    --------\n", "    transformed_points : numpy.n<PERSON><PERSON>\n", "        Transformed point cloud of shape (N, 3)\n", "    \"\"\"\n", "    # Apply rotation\n", "    rotated_points = np.dot(points, R.T)\n", "    \n", "    # Apply translation\n", "    transformed_points = rotated_points + t\n", "    \n", "    return transformed_points"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_training_data(point_cloud, num_samples=1000, num_points=1024,\n", "                          rotation_range=np.pi/4, translation_range=0.5,\n", "                          add_noise=True, noise_std=0.01):\n", "    \"\"\"\n", "    Generates training data for point cloud alignment by applying random transformations.\n", "    \n", "    Parameters:\n", "    -----------\n", "    point_cloud : numpy.n<PERSON>ray\n", "        Base point cloud of shape (N, 3)\n", "    num_samples : int\n", "        Number of training samples to generate\n", "    num_points : int\n", "        Number of points to sample from each point cloud\n", "    rotation_range : float\n", "        Maximum rotation angle in radians\n", "    translation_range : float\n", "        Maximum translation distance\n", "    add_noise : bool\n", "        Whether to add Gaussian noise to the point clouds\n", "    noise_std : float\n", "        Standard deviation of the Gaussian noise\n", "    \n", "    Returns:\n", "    --------\n", "    source_clouds : numpy.ndarray\n", "        Source point clouds of shape (num_samples, num_points, 3)\n", "    target_clouds : numpy.n<PERSON><PERSON>\n", "        Target point clouds of shape (num_samples, num_points, 3)\n", "    rotations_quat : numpy.n<PERSON><PERSON>\n", "        Quaternion rotations of shape (num_samples, 4)\n", "    translations : numpy.n<PERSON><PERSON>\n", "        Translations of shape (num_samples, 3)\n", "    \"\"\"\n", "    # Initialize arrays to store data\n", "    source_clouds = np.zeros((num_samples, num_points, 3))\n", "    target_clouds = np.zeros((num_samples, num_points, 3))\n", "    rotations_quat = np.zeros((num_samples, 4))\n", "    translations = np.zeros((num_samples, 3))\n", "    \n", "    for i in range(num_samples):\n", "        # Randomly sample points from the base point cloud\n", "        if point_cloud.shape[0] > num_points:\n", "            indices = np.random.choice(point_cloud.shape[0], num_points, replace=False)\n", "            sampled_points = point_cloud[indices]\n", "        else:\n", "            # If not enough points, use all points and duplicate some\n", "            indices = np.random.choice(point_cloud.shape[0], num_points, replace=True)\n", "            sampled_points = point_cloud[indices]\n", "        \n", "        # Generate random transformation\n", "        R, t, _, quaternion = generate_random_transformation(\n", "            rotation_range=rotation_range,\n", "            translation_range=translation_range\n", "        )\n", "        \n", "        # Apply transformation to create target point cloud\n", "        transformed_points = apply_transformation(sampled_points, R, t)\n", "        \n", "        # Add noise if specified\n", "        if add_noise:\n", "            source_noise = np.random.normal(0, noise_std, size=sampled_points.shape)\n", "            target_noise = np.random.normal(0, noise_std, size=transformed_points.shape)\n", "            source_points_noisy = sampled_points + source_noise\n", "            target_points_noisy = transformed_points + target_noise\n", "        else:\n", "            source_points_noisy = sampled_points\n", "            target_points_noisy = transformed_points\n", "        \n", "        # Store data\n", "        source_clouds[i] = source_points_noisy\n", "        target_clouds[i] = target_points_noisy\n", "        rotations_quat[i] = quaternion\n", "        translations[i] = t\n", "        \n", "        # Print progress\n", "        if (i+1) % 100 == 0 or i == 0:\n", "            print(f\"Generated {i+1}/{num_samples} training samples\")\n", "    \n", "    return source_clouds, target_clouds, rotations_quat, translations"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1 Custom Loss Functions\n", "\n", "We need to define custom loss functions for rotation (quaternion) and translation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def quaternion_distance_loss(y_true, y_pred):\n", "    \"\"\"\n", "    Computes the distance between two quaternions.\n", "    The distance is 1 - |q1.q2| where q1.q2 is the dot product.\n", "    \n", "    Parameters:\n", "    -----------\n", "    y_true : tf.<PERSON><PERSON>\n", "        Ground truth quaternions of shape (batch_size, 4)\n", "    y_pred : tf.<PERSON><PERSON>\n", "        Predicted quaternions of shape (batch_size, 4)\n", "    \n", "    Returns:\n", "    --------\n", "    loss : tf.<PERSON><PERSON>\n", "        Quaternion distance loss\n", "    \"\"\"\n", "    # Normalize quaternions\n", "    y_true_normalized = tf.math.l2_normalize(y_true, axis=-1)\n", "    y_pred_normalized = tf.math.l2_normalize(y_pred, axis=-1)\n", "    \n", "    # Compute dot product\n", "    dot_product = tf.reduce_sum(y_true_normalized * y_pred_normalized, axis=-1)\n", "    \n", "    # Take absolute value (q and -q represent the same rotation)\n", "    dot_product_abs = tf.abs(dot_product)\n", "    \n", "    # Compute distance\n", "    distance = 1.0 - dot_product_abs\n", "    \n", "    return distance\n", "\n", "def translation_loss(y_true, y_pred):\n", "    \"\"\"\n", "    Computes the mean squared error between true and predicted translations.\n", "    \n", "    Parameters:\n", "    -----------\n", "    y_true : tf.<PERSON><PERSON>\n", "        Ground truth translations of shape (batch_size, 3)\n", "    y_pred : tf.<PERSON><PERSON>\n", "        Predicted translations of shape (batch_size, 3)\n", "    \n", "    Returns:\n", "    --------\n", "    loss : tf.<PERSON><PERSON>\n", "        Translation loss (MSE)\n", "    \"\"\"\n", "    return tf.reduce_mean(tf.square(y_true - y_pred), axis=-1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Training Loop\n", "\n", "Now we'll define a function to train the neural network model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train_model(model, train_data, val_data, epochs=50, batch_size=32, learning_rate=0.001, output_dir=None):\n", "    \"\"\"\n", "    Trains the neural network model for point cloud alignment.\n", "    \n", "    Parameters:\n", "    -----------\n", "    model : tf.keras.Model\n", "        The neural network model\n", "    train_data : tuple\n", "        Tuple of (source_clouds, target_clouds, rotations_quat, translations) for training\n", "    val_data : tuple\n", "        Tuple of (source_clouds, target_clouds, rotations_quat, translations) for validation\n", "    epochs : int\n", "        Number of training epochs\n", "    batch_size : int\n", "        Batch size for training\n", "    learning_rate : float\n", "        Initial learning rate\n", "    output_dir : str, optional\n", "        Directory to save model checkpoints\n", "    \n", "    Returns:\n", "    --------\n", "    history : tf.keras.callbacks.History\n", "        Training history\n", "    \"\"\"\n", "    # Unpack data\n", "    train_source, train_target, train_rot, train_trans = train_data\n", "    val_source, val_target, val_rot, val_trans = val_data\n", "    \n", "    # Compile the model\n", "    model.compile(\n", "        optimizer=tf.keras.optimizers.<PERSON>(learning_rate=learning_rate),\n", "        loss={\n", "            'normalized_quaternion': quaternion_distance_loss,\n", "            'translation': translation_loss\n", "        },\n", "        loss_weights={\n", "            'normalized_quaternion': 1.0,\n", "            'translation': 1.0\n", "        }\n", "    )\n", "    \n", "    # Define callbacks\n", "    callbacks = [\n", "        tf.keras.callbacks.EarlyStopping(\n", "            monitor='val_loss',\n", "            patience=10,\n", "            restore_best_weights=True\n", "        ),\n", "        tf.keras.callbacks.ReduceLROnPlateau(\n", "            monitor='val_loss',\n", "            factor=0.5,\n", "            patience=5,\n", "            min_lr=1e-6\n", "        )\n", "    ]\n", "    \n", "    # Add model checkpoint if output directory is provided\n", "    if output_dir is not None:\n", "        os.makedirs(output_dir, exist_ok=True)\n", "        callbacks.append(\n", "            tf.keras.callbacks.ModelCheckpoint(\n", "                os.path.join(output_dir, 'point_cloud_alignment_model.h5'),\n", "                monitor='val_loss',\n", "                save_best_only=True,\n", "                verbose=1\n", "            )\n", "        )\n", "    \n", "    # Train the model\n", "    print(\"\\n=== Training Neural Network Model ===\\n\")\n", "    print(f\"Training on {train_source.shape[0]} samples, validating on {val_source.shape[0]} samples\")\n", "    print(f\"Epochs: {epochs}, Batch size: {batch_size}, Initial learning rate: {learning_rate}\")\n", "    print(\"This may take a while...\")\n", "    \n", "    history = model.fit(\n", "        x=[train_source, train_target],\n", "        y=[train_rot, train_trans],\n", "        validation_data=([val_source, val_target], [val_rot, val_trans]),\n", "        epochs=epochs,\n", "        batch_size=batch_size,\n", "        callbacks=callbacks,\n", "        verbose=1\n", "    )\n", "    \n", "    return history"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 Visualizing Training Results\n", "\n", "Let's define a function to visualize the training and validation metrics."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_training_history(history):\n", "    \"\"\"\n", "    Plots the training and validation metrics.\n", "    \n", "    Parameters:\n", "    -----------\n", "    history : tf.keras.callbacks.History\n", "        Training history\n", "    \"\"\"\n", "    plt.figure(figsize=(15, 5))\n", "    \n", "    # Plot total loss\n", "    plt.subplot(1, 3, 1)\n", "    plt.plot(history.history['loss'], label='Training Loss')\n", "    plt.plot(history.history['val_loss'], label='Validation Loss')\n", "    plt.title('Total Loss')\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Loss')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # Plot rotation loss\n", "    plt.subplot(1, 3, 2)\n", "    plt.plot(history.history['normalized_quaternion_loss'], label='Training')\n", "    plt.plot(history.history['val_normalized_quaternion_loss'], label='Validation')\n", "    plt.title('Rotation Loss')\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Loss')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # Plot translation loss\n", "    plt.subplot(1, 3, 3)\n", "    plt.plot(history.history['translation_loss'], label='Training')\n", "    plt.plot(history.history['val_translation_loss'], label='Validation')\n", "    plt.title('Translation Loss')\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Loss')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Print final metrics\n", "    print(\"\\nFinal Training Metrics:\")\n", "    print(f\"Total Loss: {history.history['loss'][-1]:.6f}\")\n", "    print(f\"Rotation Loss: {history.history['normalized_quaternion_loss'][-1]:.6f}\")\n", "    print(f\"Translation Loss: {history.history['translation_loss'][-1]:.6f}\")\n", "    \n", "    print(\"\\nFinal Validation Metrics:\")\n", "    print(f\"Total Loss: {history.history['val_loss'][-1]:.6f}\")\n", "    print(f\"Rotation Loss: {history.history['val_normalized_quaternion_loss'][-1]:.6f}\")\n", "    print(f\"Translation Loss: {history.history['val_translation_loss'][-1]:.6f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. ICP Implementation\n", "\n", "Before implementing the hybrid approach, we need to implement the Iterative Closest Point (ICP) algorithm for point cloud alignment. ICP is a traditional method that iteratively finds the closest points between two point clouds and estimates the transformation that minimizes the distance between them."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def nearest_neighbor(source, target):\n", "    \"\"\"\n", "    Finds the nearest neighbor in target for each point in source.\n", "    \n", "    Parameters:\n", "    -----------\n", "    source : numpy.ndarray\n", "        Source point cloud of shape (N, 3)\n", "    target : numpy.n<PERSON><PERSON>\n", "        Target point cloud of shape (M, 3)\n", "    \n", "    Returns:\n", "    --------\n", "    distances : numpy.ndarray\n", "        Distances to nearest neighbors of shape (N,)\n", "    indices : numpy.ndarray\n", "        Indices of nearest neighbors of shape (N,)\n", "    \"\"\"\n", "    # Use Open3D for efficient nearest neighbor search if available\n", "    if O3D_SUPPORT:\n", "        target_pcd = o3d.geometry.PointCloud()\n", "        target_pcd.points = o3d.utility.Vector3dVector(target)\n", "        \n", "        # Build KD-tree\n", "        kdtree = o3d.geometry.KDTreeFlann(target_pcd)\n", "        \n", "        # Find nearest neighbors\n", "        indices = np.zeros(source.shape[0], dtype=int)\n", "        distances = np.zeros(source.shape[0])\n", "        \n", "        for i in range(source.shape[0]):\n", "            _, idx, dist = kdtree.search_knn_vector_3d(source[i], 1)\n", "            indices[i] = idx[0]\n", "            distances[i] = np.sqrt(dist[0])\n", "        \n", "        return distances, indices\n", "    else:\n", "        # Fallback to sklearn if Open3D is not available\n", "        from sklearn.neighbors import NearestNeighbors\n", "        \n", "        # Build KD-tree\n", "        nn = NearestNeighbors(n_neighbors=1, algorithm='kd_tree').fit(target)\n", "        \n", "        # Find nearest neighbors\n", "        distances, indices = nn.kneighbors(source)\n", "        \n", "        return distances.ravel(), indices.ravel()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def best_fit_transform(source, target):\n", "    \"\"\"\n", "    Calculates the least-squares best-fit transform between corresponding 3D points.\n", "    \n", "    Parameters:\n", "    -----------\n", "    source : numpy.ndarray\n", "        Source points of shape (N, 3)\n", "    target : numpy.n<PERSON><PERSON>\n", "        Target points of shape (N, 3)\n", "    \n", "    Returns:\n", "    --------\n", "    T : numpy.n<PERSON><PERSON>\n", "        4x4 homogeneous transformation matrix\n", "    R : numpy.n<PERSON><PERSON>\n", "        3x3 rotation matrix\n", "    t : numpy.n<PERSON><PERSON>\n", "        3x1 translation vector\n", "    \"\"\"\n", "    assert source.shape == target.shape, \"Source and target point clouds must have the same shape\"\n", "    \n", "    # Get number of dimensions\n", "    dim = source.shape[1]\n", "    \n", "    # Center both point clouds\n", "    source_centroid = np.mean(source, axis=0)\n", "    target_centroid = np.mean(target, axis=0)\n", "    source_centered = source - source_centroid\n", "    target_centered = target - target_centroid\n", "    \n", "    # Compute covariance matrix\n", "    H = np.dot(source_centered.T, target_centered)\n", "    \n", "    # Singular Value Decomposition\n", "    U, S, Vt = np.linalg.svd(H)\n", "    \n", "    # Compute rotation matrix\n", "    R = np.dot(Vt.T, U.T)\n", "    \n", "    # Special reflection case\n", "    if np.linalg.det(R) < 0:\n", "        Vt[-1, :] *= -1\n", "        R = np.dot(Vt.T, U.T)\n", "    \n", "    # Compute translation vector\n", "    t = target_centroid - np.dot(R, source_centroid)\n", "    \n", "    # Create homogeneous transformation matrix\n", "    T = np.identity(dim + 1)\n", "    T[:dim, :dim] = R\n", "    T[:dim, dim] = t\n", "    \n", "    return T, R, t"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def icp_algorithm(source, target, max_iterations=20, tolerance=1e-6, verbose=True):\n", "    \"\"\"\n", "    Implements the Iterative Closest Point (ICP) algorithm for point cloud alignment.\n", "    \n", "    Parameters:\n", "    -----------\n", "    source : numpy.ndarray\n", "        Source point cloud of shape (N, 3)\n", "    target : numpy.n<PERSON><PERSON>\n", "        Target point cloud of shape (M, 3)\n", "    max_iterations : int, optional\n", "        Maximum number of iterations\n", "    tolerance : float, optional\n", "        Convergence tolerance\n", "    verbose : bool, optional\n", "        Whether to print progress information\n", "    \n", "    Returns:\n", "    --------\n", "    T : numpy.n<PERSON><PERSON>\n", "        4x4 homogeneous transformation matrix\n", "    transformed_source : numpy.ndarray\n", "        Transformed source point cloud\n", "    mean_error : float\n", "        Mean distance between corresponding points\n", "    iterations : int\n", "        Number of iterations performed\n", "    \"\"\"\n", "    # Make a copy of the source point cloud\n", "    source_copy = source.copy()\n", "    prev_error = 0\n", "    \n", "    # Initialize transformation matrix\n", "    T = np.identity(4)\n", "    \n", "    if verbose:\n", "        print(\"Starting ICP algorithm...\")\n", "    \n", "    for i in range(max_iterations):\n", "        # Find nearest neighbors\n", "        distances, indices = nearest_neighbor(source_copy, target)\n", "        \n", "        # Compute mean error\n", "        mean_error = np.mean(distances)\n", "        \n", "        # Check for convergence\n", "        if abs(prev_error - mean_error) < tolerance:\n", "            if verbose:\n", "                print(f\"Converged after {i+1} iterations. Mean error: {mean_error:.6f}\")\n", "            break\n", "        \n", "        prev_error = mean_error\n", "        \n", "        # Get corresponding points\n", "        corresponding_target_points = target[indices]\n", "        \n", "        # Compute transformation\n", "        T_iter, R, t = best_fit_transform(source_copy, corresponding_target_points)\n", "        \n", "        # Update transformation matrix\n", "        T = np.dot(T_iter, T)\n", "        \n", "        # Apply transformation\n", "        source_copy = np.dot(source, T[:3, :3].T) + T[:3, 3]\n", "        \n", "        if verbose and (i+1) % 5 == 0:\n", "            print(f\"Iteration {i+1}/{max_iterations}, Mean error: {mean_error:.6f}\")\n", "    \n", "    if i == max_iterations - 1 and verbose:\n", "        print(f\"Reached maximum iterations ({max_iterations}). Mean error: {mean_error:.6f}\")\n", "    \n", "    return T, source_copy, mean_error, i+1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def save_aligned_point_cloud(points, file_path, format=None, colors=None, metadata=None):\n", "    \"\"\"\n", "    Save aligned point cloud to file in the specified format.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud data (N x 3)\n", "    file_path : str\n", "        Path to save the point cloud\n", "    format : str, optional\n", "        Force a specific file format ('las', 'ply', 'pcd')\n", "        If None, will be determined from file extension\n", "    colors : numpy.ndarray, optional\n", "        Point colors (N x 3), values should be in range [0, 1]\n", "    metadata : dict, optional\n", "        Additional metadata to include in the file\n", "        \n", "    Returns:\n", "    --------\n", "    bool\n", "        True if successful, False otherwise\n", "    \"\"\"\n", "    # Use our custom point cloud I/O utilities if available\n", "    if POINT_CLOUD_IO_SUPPORT:\n", "        return save_point_cloud(points, file_path, format, colors, metadata)\n", "    \n", "    # Fallback to built-in Open3D for PLY/PCD\n", "    try:\n", "        # Create Open3D point cloud\n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(points)\n", "        \n", "        # Add colors if provided\n", "        if colors is not None:\n", "            pcd.colors = o3d.utility.Vector3dVector(colors)\n", "        \n", "        # Save to file\n", "        if file_path.lower().endswith('.ply'):\n", "            o3d.io.write_point_cloud(file_path, pcd)\n", "        elif file_path.lower().endswith('.pcd'):\n", "            o3d.io.write_point_cloud(file_path, pcd)\n", "        else:\n", "            # Default to PLY if no extension or unknown extension\n", "            file_path = file_path + '.ply' if '.' not in file_path else file_path\n", "            o3d.io.write_point_cloud(file_path, pcd)\n", "        \n", "        logger.info(f\"Saved point cloud with {len(points)} points to {file_path}\")\n", "        return True\n", "    except Exception as e:\n", "        logger.error(f\"Error saving point cloud to {file_path}: {e}\")\n", "        return False\n", "\n", "def align_point_clouds_neural(source_pc, target_pc, model, num_points=1024, save_output=False, output_dir=None, output_format='las'):\n", "    \"\"\"\n", "    Aligns source point cloud to target point cloud using the neural network model.\n", "    \n", "    Parameters:\n", "    -----------\n", "    source_pc : numpy.ndarray\n", "        Source point cloud of shape (N, 3)\n", "    target_pc : numpy.ndarray\n", "        Target point cloud of shape (M, 3)\n", "    model : tf.keras.Model\n", "        Trained neural network model\n", "    num_points : int\n", "        Number of points to sample from each point cloud\n", "    save_output : bool\n", "        Whether to save the aligned point cloud\n", "    output_dir : str, optional\n", "        Directory to save output (required if save_output=True)\n", "    output_format : str, optional\n", "        Format to save output ('las', 'ply', 'pcd')\n", "    \n", "    Returns:\n", "    --------\n", "    aligned_source : numpy.ndarray\n", "        Aligned source point cloud\n", "    R : numpy.n<PERSON><PERSON>\n", "        3x3 rotation matrix\n", "    t : numpy.n<PERSON><PERSON>\n", "        3x1 translation vector\n", "    \"\"\"\n", "    # Check if we need to save output\n", "    if save_output and output_dir is None:\n", "        logger.warning(\"output_dir must be specified when save_output=True. Output will not be saved.\")\n", "        save_output = False\n", "    \n", "    # Create output directory if it doesn't exist\n", "    if save_output and not os.path.exists(output_dir):\n", "        os.makedirs(output_dir, exist_ok=True)\n", "        logger.info(f\"Created output directory: {output_dir}\")\n", "    \n", "    # Sample points if necessary\n", "    if source_pc.shape[0] > num_points:\n", "        source_indices = np.random.choice(source_pc.shape[0], num_points, replace=False)\n", "        source_sample = source_pc[source_indices]\n", "    else:\n", "        source_indices = np.random.choice(source_pc.shape[0], num_points, replace=True)\n", "        source_sample = source_pc[source_indices]\n", "    \n", "    if target_pc.shape[0] > num_points:\n", "        target_indices = np.random.choice(target_pc.shape[0], num_points, replace=False)\n", "        target_sample = target_pc[target_indices]\n", "    else:\n", "        target_indices = np.random.choice(target_pc.shape[0], num_points, replace=True)\n", "        target_sample = target_pc[target_indices]\n", "    \n", "    # Reshape for model input (add batch dimension)\n", "    source_input = np.expand_dims(source_sample, axis=0)\n", "    target_input = np.expand_dims(target_sample, axis=0)\n", "    \n", "    # Predict transformation\n", "    quaternion, translation = model.predict([source_input, target_input])\n", "    \n", "    # Convert quaternion to rotation matrix\n", "    quaternion = quaternion[0]  # Remove batch dimension\n", "    translation = translation[0]  # Remove batch dimension\n", "    \n", "    # Convert quaternion to rotation matrix\n", "    R = t3d_quaternions.quat2mat(quaternion)\n", "    \n", "    # Apply transformation to the entire source point cloud\n", "    aligned_source = np.dot(source_pc, R.T) + translation\n", "    \n", "    # Save output if requested\n", "    if save_output:\n", "        # Create metadata for the output\n", "        metadata = {\n", "            'method': 'neural_network',\n", "            'transformation': {\n", "                'quaternion': quaternion.tolist(),\n", "                'translation': translation.tolist(),\n", "                'rotation_matrix': <PERSON><PERSON>tolist()\n", "            },\n", "            'coordinate_system': {\n", "                'description': 'Right-handed coordinate system with Z-axis pointing up',\n", "                'x_axis': 'East',\n", "                'y_axis': 'North',\n", "                'z_axis': 'Up',\n", "                'units': 'meters'\n", "            }\n", "        }\n", "        \n", "        # Save the aligned point cloud\n", "        file_path = os.path.join(output_dir, f'aligned_source_neural.{output_format}')\n", "        success = save_aligned_point_cloud(aligned_source, file_path, format=output_format, metadata=metadata)\n", "        \n", "        if success:\n", "            logger.info(f\"Saved neural network alignment result to {file_path}\")\n", "            \n", "            # Also save in PLY format for visualization\n", "            if output_format != 'ply':\n", "                ply_file_path = os.path.join(output_dir, 'aligned_source_neural.ply')\n", "                save_aligned_point_cloud(aligned_source, ply_file_path, format='ply')\n", "                logger.info(f\"Saved neural network alignment result in PLY format for visualization: {ply_file_path}\")\n", "    \n", "    return aligned_source, R, translation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Hybrid Method (Neural + ICP)\n", "\n", "While this notebook focuses on the neural network approach, we also include a hybrid method implementation that can be used if needed. The hybrid approach combines the strengths of both neural networks and ICP:\n", "1. Neural network for coarse alignment (robust to poor initialization)\n", "2. ICP for fine-tuning (precise local alignment)\n", "\n", "This approach often achieves better results than either method alone, especially for challenging point cloud pairs with partial overlaps or significant initial misalignment.\n", "\n", "Note: For a comprehensive comparison of ICP, Neural Network, and Hybrid methods, please refer to the `point_cloud_alignment_benchmark_comparison.ipynb` notebook."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def align_point_clouds_hybrid(source_pc, target_pc, model, num_points=1024, icp_max_iterations=20, save_output=False, output_dir=None, output_format='las'):\n", "    \"\"\"\n", "    Aligns source point cloud to target point cloud using a hybrid approach (neural network + ICP).\n", "    \n", "    Parameters:\n", "    -----------\n", "    source_pc : numpy.ndarray\n", "        Source point cloud of shape (N, 3)\n", "    target_pc : numpy.ndarray\n", "        Target point cloud of shape (M, 3)\n", "    model : tf.keras.Model\n", "        Trained neural network model\n", "    num_points : int\n", "        Number of points to sample for neural network\n", "    icp_max_iterations : int\n", "        Maximum number of ICP iterations\n", "    save_output : bool\n", "        Whether to save the aligned point cloud\n", "    output_dir : str, optional\n", "        Directory to save output (required if save_output=True)\n", "    output_format : str, optional\n", "        Format to save output ('las', 'ply', 'pcd')\n", "    \n", "    Returns:\n", "    --------\n", "    aligned_source : numpy.ndarray\n", "        Aligned source point cloud\n", "    T_combined : numpy.n<PERSON><PERSON>\n", "        4x4 combined transformation matrix\n", "    neural_time : float\n", "        Time taken by neural network alignment\n", "    icp_time : float\n", "        Time taken by ICP refinement\n", "    \"\"\"\n", "    # Check if we need to save output\n", "    if save_output and output_dir is None:\n", "        logger.warning(\"output_dir must be specified when save_output=True. Output will not be saved.\")\n", "        save_output = False\n", "    \n", "    # Create output directory if it doesn't exist\n", "    if save_output and not os.path.exists(output_dir):\n", "        os.makedirs(output_dir, exist_ok=True)\n", "        logger.info(f\"Created output directory: {output_dir}\")\n", "    \n", "    # Step 1: Neural network alignment\n", "    neural_start_time = time.time()\n", "    aligned_source_neural, R_neural, t_neural = align_point_clouds_neural(\n", "        source_pc, target_pc, model, num_points=num_points,\n", "        save_output=save_output, output_dir=output_dir, output_format=output_format\n", "    )\n", "    neural_end_time = time.time()\n", "    neural_time = neural_end_time - neural_start_time\n", "    \n", "    # Step 2: ICP refinement\n", "    icp_start_time = time.time()\n", "    T_icp, aligned_source_hybrid, icp_error, icp_iterations = icp_algorithm(\n", "        aligned_source_neural, target_pc, max_iterations=icp_max_iterations, tolerance=1e-6, verbose=False,\n", "        save_intermediate=save_output, output_dir=output_dir, output_format=output_format\n", "    )\n", "    icp_end_time = time.time()\n", "    icp_time = icp_end_time - icp_start_time\n", "    \n", "    # Create combined transformation matrix\n", "    T_neural = np.identity(4)\n", "    T_neural[:3, :3] = R_neural\n", "    T_neural[:3, 3] = t_neural\n", "    T_combined = np.dot(T_icp, T_neural)\n", "    \n", "    # Save the final hybrid result if requested\n", "    if save_output:\n", "        # Create metadata for the output\n", "        metadata = {\n", "            'method': 'hybrid_neural_icp',\n", "            'transformation': {\n", "                'neural_quaternion': R_neural.tolist(),\n", "                'neural_translation': t_neural.tolist(),\n", "                'icp_matrix': T_icp.tolist(),\n", "                'combined_matrix': T_combined.tolist()\n", "            },\n", "            'timing': {\n", "                'neural_time_seconds': neural_time,\n", "                'icp_time_seconds': icp_time,\n", "                'total_time_seconds': neural_time + icp_time\n", "            },\n", "            'icp_info': {\n", "                'iterations': icp_iterations,\n", "                'final_error': float(icp_error)\n", "            },\n", "            'coordinate_system': {\n", "                'description': 'Right-handed coordinate system with Z-axis pointing up',\n", "                'x_axis': 'East',\n", "                'y_axis': 'North',\n", "                'z_axis': 'Up',\n", "                'units': 'meters'\n", "            }\n", "        }\n", "        \n", "        # Save the aligned point cloud\n", "        file_path = os.path.join(output_dir, f'aligned_source_hybrid.{output_format}')\n", "        success = save_aligned_point_cloud(aligned_source_hybrid, file_path, format=output_format, metadata=metadata)\n", "        \n", "        if success:\n", "            logger.info(f\"Saved hybrid alignment result to {file_path}\")\n", "            \n", "            # Also save in PLY format for visualization\n", "            if output_format != 'ply':\n", "                ply_file_path = os.path.join(output_dir, 'aligned_source_hybrid.ply')\n", "                save_aligned_point_cloud(aligned_source_hybrid, ply_file_path, format='ply')\n", "                logger.info(f\"Saved hybrid alignment result in PLY format for visualization: {ply_file_path}\")\n", "    \n", "    return aligned_source_hybrid, T_combined, neural_time, icp_time"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Conc<PERSON>\n", "\n", "In this notebook, we've implemented a neural network approach for point cloud alignment and a hybrid method that combines neural networks with ICP. The neural network provides robust coarse alignment, while ICP refines the result for precise alignment.\n", "\n", "This approach is particularly useful for aligning CAD/BIM models with scan data, where traditional methods might struggle with initialization or partial overlaps."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Example: Training and Evaluation\n", "\n", "Let's put everything together and demonstrate how to train and evaluate the model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define paths to your point cloud files\n", "if IN_COLAB:\n", "    # Google Drive paths\n", "    base_path = '/content/gdrive/MyDrive/pc-experiment'\n", "    ifc_file = os.path.join(base_path, 'GRE.EEC.S.00.IT.P.14353.00.265.ifc')\n", "    las_file = os.path.join(base_path, 'scan_data.las')  # Adjust this to your actual LAS file\n", "else:\n", "    # Local paths - adjust as needed\n", "    base_path = 'data/pc-experiment'\n", "    ifc_file = os.path.join(base_path, 'GRE.EEC.S.00.IT.P.14353.00.265.ifc')\n", "    las_file = os.path.join(base_path, 'scan_data.las')  # Adjust this to your actual LAS file\n", "\n", "# Create output directory if it doesn't exist\n", "output_dir = os.path.join(base_path, 'output')\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "# Define paths for converted point clouds\n", "ifc_pointcloud_path = os.path.join(output_dir, 'ifc_pointcloud.ply')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a synthetic point cloud for demonstration\n", "def create_synthetic_point_cloud(num_points=5000):\n", "    \"\"\"\n", "    Creates a synthetic point cloud for demonstration purposes.\n", "    \n", "    Parameters:\n", "    -----------\n", "    num_points : int\n", "        Number of points to generate\n", "    \n", "    Returns:\n", "    --------\n", "    point_cloud : numpy.n<PERSON>ray\n", "        Synthetic point cloud of shape (num_points, 3)\n", "    \"\"\"\n", "    # Generate random points in a cube\n", "    points = np.random.uniform(-1, 1, size=(num_points, 3))\n", "    \n", "    # Add some structure (e.g., a sphere)\n", "    sphere_points = np.random.normal(0, 0.1, size=(num_points // 2, 3))\n", "    sphere_points = sphere_points / np.linalg.norm(sphere_points, axis=1, keepdims=True) * 0.5\n", "    \n", "    # Combine points\n", "    combined_points = np.vstack([points[:num_points // 2], sphere_points])\n", "    \n", "    return combined_points\n", "\n", "# Functions to load point cloud data from different sources\n", "def read_las_file(filename, num_points=None):\n", "    \"\"\"\n", "    Reads a LAS file and returns the points as a numpy array.\n", "    \"\"\"\n", "    logger.info(f\"Reading LAS file: {filename}\")\n", "    if not LAS_SUPPORT:\n", "        logger.error(\"LAS support is not available. Please install laspy.\")\n", "        return None\n", "        \n", "    try:\n", "        las_data = laspy.read(filename)\n", "\n", "        # Determine the number of points to read\n", "        if num_points is None:\n", "            num_points = len(las_data.x)\n", "        else:\n", "            num_points = min(num_points, len(las_data.x))\n", "\n", "        # Ensure data types are compatible and aligned\n", "        x = np.array(las_data.x[:num_points], dtype=np.float64, copy=False)\n", "        y = np.array(las_data.y[:num_points], dtype=np.float64, copy=False)\n", "        z = np.array(las_data.z[:num_points], dtype=np.float64, copy=False)\n", "\n", "        # Stack XYZ coordinates into a single numpy array\n", "        points = np.column_stack((x, y, z))\n", "        logger.info(f\"Loaded {points.shape[0]} points from {filename}\")\n", "        return points\n", "    except Exception as e:\n", "        logger.error(f\"Error reading LAS file '{filename}': {e}\")\n", "        return None\n", "\n", "def read_ifc_file(filename):\n", "    \"\"\"\n", "    Reads an IFC file and extracts point cloud data.\n", "    \"\"\"\n", "    logger.info(f\"Reading IFC file: {filename}\")\n", "    \n", "    if not IFC_SUPPORT:\n", "        logger.error(\"IFC support is not available. Please install ifcopenshell.\")\n", "        return None\n", "    \n", "    try:\n", "        # Load IFC file\n", "        ifc_file = ifcopenshell.open(filename)\n", "        \n", "        # Set up geometry settings\n", "        settings = ifcopenshell.geom.settings()\n", "        settings.set(settings.USE_WORLD_COORDS, True)\n", "        \n", "        # Extract all vertices from all shapes\n", "        all_vertices = []\n", "        \n", "        # Process all products in the IFC file\n", "        products = ifc_file.by_type(\"IfcProduct\")\n", "        logger.info(f\"Found {len(products)} products in IFC file\")\n", "        \n", "        for product in products:\n", "            if product.Representation:\n", "                try:\n", "                    # Create a shape from the product\n", "                    shape = ifcopenshell.geom.create_shape(settings, product)\n", "                    \n", "                    # Get the vertices\n", "                    verts = shape.geometry.verts\n", "                    if verts:\n", "                        # Reshape to (N, 3) array\n", "                        points = np.array(verts).reshape(-1, 3)\n", "                        all_vertices.append(points)\n", "                except Exception as e:\n", "                    # Skip products that can't be processed\n", "                    continue\n", "        \n", "        if all_vertices:\n", "            # Combine all vertices into a single array\n", "            combined_vertices = np.vstack(all_vertices)\n", "            logger.info(f\"Loaded {combined_vertices.shape[0]} points from {filename}\")\n", "            return combined_vertices\n", "        else:\n", "            logger.error(f\"No geometry found in IFC file: {filename}\")\n", "            return None\n", "    except Exception as e:\n", "        logger.error(f\"Error reading IFC file '{filename}': {e}\")\n", "        return None\n", "\n", "def convert_ifc_to_pointcloud(filename, element_types=None, sampling_rate=1.0, save_to_file=None, visualize=False):\n", "    \"\"\"\n", "    Enhanced function to convert an IFC file to a point cloud with more control options.\n", "    \"\"\"\n", "    logger.info(f\"Converting IFC file to point cloud: {filename}\")\n", "    \n", "    if not IFC_SUPPORT:\n", "        logger.error(\"IFC support is not available. Please install ifcopenshell.\")\n", "        return None\n", "    \n", "    try:\n", "        # Load IFC file\n", "        ifc_file = ifcopenshell.open(filename)\n", "        \n", "        # Set up geometry settings\n", "        settings = ifcopenshell.geom.settings()\n", "        settings.set(settings.USE_WORLD_COORDS, True)\n", "        \n", "        # Get all products or filter by element types\n", "        if element_types:\n", "            products = []\n", "            for element_type in element_types:\n", "                products.extend(ifc_file.by_type(element_type))\n", "            logger.info(f\"Found {len(products)} products of types {element_types}\")\n", "        else:\n", "            products = ifc_file.by_type(\"IfcProduct\")\n", "            logger.info(f\"Found {len(products)} products in IFC file\")\n", "        \n", "        # Extract all vertices from all shapes\n", "        all_vertices = []\n", "        product_colors = []  # For visualization\n", "        \n", "        # Process products\n", "        for product in products:\n", "            if product.Representation:\n", "                try:\n", "                    # Create a shape from the product\n", "                    shape = ifcopenshell.geom.create_shape(settings, product)\n", "                    \n", "                    # Get the vertices\n", "                    verts = shape.geometry.verts\n", "                    if verts:\n", "                        # Reshape to (N, 3) array\n", "                        points = np.array(verts).reshape(-1, 3)\n", "                        \n", "                        # Apply sampling if needed\n", "                        if sampling_rate < 1.0:\n", "                            num_points = max(1, int(points.shape[0] * sampling_rate))\n", "                            indices = np.random.choice(points.shape[0], num_points, replace=False)\n", "                            points = points[indices]\n", "                        \n", "                        all_vertices.append(points)\n", "                        \n", "                        # Get color for visualization\n", "                        if visualize:\n", "                            # Generate a random color for this product\n", "                            random_color = np.random.rand(3)\n", "                            product_colors.append(np.tile(random_color, (points.shape[0], 1)))\n", "                except Exception as e:\n", "                    # Skip products that can't be processed\n", "                    logger.warning(f\"Skipping product {product.id()}: {str(e)}\")\n", "                    continue\n", "        \n", "        if not all_vertices:\n", "            logger.error(f\"No geometry found in IFC file: {filename}\")\n", "            return None\n", "            \n", "        # Combine all vertices into a single array\n", "        combined_vertices = np.vstack(all_vertices)\n", "        logger.info(f\"Extracted {combined_vertices.shape[0]} points from {filename}\")\n", "        \n", "        # Visualize if requested\n", "        if visualize and combined_vertices.shape[0] > 0 and O3D_SUPPORT:\n", "            try:\n", "                # Create Open3D point cloud\n", "                pcd = o3d.geometry.PointCloud()\n", "                pcd.points = o3d.utility.Vector3dVector(combined_vertices)\n", "                \n", "                # Add colors if available\n", "                if product_colors:\n", "                    combined_colors = np.vstack(product_colors)\n", "                    pcd.colors = o3d.utility.Vector3dVector(combined_colors)\n", "                \n", "                # Visualize\n", "                logger.info(\"Visualizing IFC model as point cloud...\")\n", "                o3d.visualization.draw_geometries([pcd], window_name=\"IFC Model as Point Cloud\")\n", "            except Exception as e:\n", "                logger.warning(f\"Visualization failed: {str(e)}\")\n", "        \n", "        # Save to file if requested\n", "        if save_to_file and combined_vertices.shape[0] > 0 and O3D_SUPPORT:\n", "            try:\n", "                # Create Open3D point cloud\n", "                pcd = o3d.geometry.PointCloud()\n", "                pcd.points = o3d.utility.Vector3dVector(combined_vertices)\n", "                \n", "                # Add colors if available\n", "                if product_colors:\n", "                    combined_colors = np.vstack(product_colors)\n", "                    pcd.colors = o3d.utility.Vector3dVector(combined_colors)\n", "                \n", "                # Save to file\n", "                if save_to_file.lower().endswith('.ply'):\n", "                    o3d.io.write_point_cloud(save_to_file, pcd)\n", "                elif save_to_file.lower().endswith('.pcd'):\n", "                    o3d.io.write_point_cloud(save_to_file, pcd)\n", "                else:\n", "                    # Default to PLY if no extension or unknown extension\n", "                    save_to_file = save_to_file + '.ply' if '.' not in save_to_file else save_to_file\n", "                    o3d.io.write_point_cloud(save_to_file, pcd)\n", "                \n", "                logger.info(f\"Saved point cloud to {save_to_file}\")\n", "            except Exception as e:\n", "                logger.warning(f\"Failed to save point cloud: {str(e)}\")\n", "        \n", "        return combined_vertices\n", "    except Exception as e:\n", "        logger.error(f\"Error converting IFC file to point cloud: {e}\")\n", "        return None\n", "\n", "# Load real-world point cloud data\n", "print(\"\\n=== Loading Real-World Point Cloud Data ===\\n\")\n", "\n", "# Check if we already have a converted IFC point cloud file\n", "if os.path.exists(ifc_pointcloud_path) and O3D_SUPPORT:\n", "    print(f\"Found existing IFC point cloud at {ifc_pointcloud_path}\")\n", "    pcd = o3d.io.read_point_cloud(ifc_pointcloud_path)\n", "    ifc_points = np.asarray(pcd.points)\n", "    print(f\"Loaded existing IFC point cloud with {ifc_points.shape[0]} points\")\n", "else:\n", "    # Convert IFC to point cloud\n", "    print(\"No existing IFC point cloud found, converting from IFC file...\")\n", "    if IFC_SUPPORT:\n", "        ifc_points = convert_ifc_to_pointcloud(\n", "            ifc_file,\n", "            element_types=None,  # Include all element types\n", "            sampling_rate=1.0,   # Use all points\n", "            save_to_file=ifc_pointcloud_path,\n", "            visualize=True       # Visualize the point cloud\n", "        )\n", "        if ifc_points is not None:\n", "            print(f\"Successfully converted IFC to point cloud with {ifc_points.shape[0]} points\")\n", "        else:\n", "            print(\"Failed to convert IFC to point cloud\")\n", "            # Fall back to synthetic point cloud\n", "            print(\"Falling back to synthetic point cloud...\")\n", "            ifc_points = create_synthetic_point_cloud(num_points=5000)\n", "    else:\n", "        print(\"IFC support not available. Using synthetic point cloud instead.\")\n", "        ifc_points = create_synthetic_point_cloud(num_points=5000)\n", "\n", "# Try to load LAS file\n", "if os.path.exists(las_file) and LAS_SUPPORT:\n", "    print(f\"\\nLoading LAS point cloud from {las_file}...\")\n", "    las_points = read_las_file(las_file)\n", "    if las_points is not None:\n", "        print(f\"Successfully loaded LAS point cloud with {las_points.shape[0]} points\")\n", "    else:\n", "        print(\"Failed to load LAS point cloud\")\n", "        # Use IFC points as both source and target\n", "        print(\"Using IFC points for both source and target...\")\n", "        las_points = ifc_points.copy()\n", "else:\n", "    print(\"\\nLAS file not found or LAS support not available.\")\n", "    print(\"Using IFC points for both source and target...\")\n", "    las_points = ifc_points.copy()\n", "\n", "# Normalize point clouds\n", "def normalize_point_cloud(points):\n", "    \"\"\"Normalizes a point cloud to have zero mean and unit variance.\"\"\"\n", "    centroid = np.mean(points, axis=0)\n", "    points_centered = points - centroid\n", "    scale = np.max(np.linalg.norm(points_centered, axis=1))\n", "    points_normalized = points_centered / scale\n", "    return points_normalized, centroid, scale\n", "\n", "print(\"\\nNormalizing point clouds...\")\n", "ifc_points_normalized, ifc_centroid, ifc_scale = normalize_point_cloud(ifc_points)\n", "las_points_normalized, las_centroid, las_scale = normalize_point_cloud(las_points)\n", "\n", "print(f\"IFC point cloud normalized: centroid={ifc_centroid}, scale={ifc_scale}\")\n", "print(f\"LAS point cloud normalized: centroid={las_centroid}, scale={las_scale}\")\n", "\n", "# Visualize the normalized point clouds\n", "if O3D_SUPPORT:\n", "    print(\"\\nVisualizing normalized point clouds...\")\n", "    \n", "    # Create Open3D point clouds\n", "    ifc_pcd = o3d.geometry.PointCloud()\n", "    ifc_pcd.points = o3d.utility.Vector3dVector(ifc_points_normalized)\n", "    ifc_pcd.paint_uniform_color([1, 0, 0])  # Red for IFC\n", "    \n", "    las_pcd = o3d.geometry.PointCloud()\n", "    las_pcd.points = o3d.utility.Vector3dVector(las_points_normalized)\n", "    las_pcd.paint_uniform_color([0, 1, 0])  # Green for LAS\n", "    \n", "    # Visualize\n", "    o3d.visualization.draw_geometries([ifc_pcd, las_pcd], window_name=\"Normalized Point Clouds\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate training data\n", "print(\"\\n=== Generating Training Data ===\\n\")\n", "\n", "# Use a smaller dataset for demonstration purposes\n", "num_training_samples = 500\n", "num_points_per_cloud = 1024\n", "\n", "# Generate training data using real-world point clouds\n", "# We'll use the normalized IFC point cloud as our base\n", "base_point_cloud = ifc_points_normalized\n", "\n", "print(f\"Using normalized IFC point cloud with {base_point_cloud.shape[0]} points as base for training data\")\n", "\n", "# Generate training data\n", "source_clouds, target_clouds, rotations_quat, translations = generate_training_data(\n", "    base_point_cloud,\n", "    num_samples=num_training_samples,\n", "    num_points=num_points_per_cloud,\n", "    rotation_range=np.pi/4,  # 45 degrees\n", "    translation_range=0.5,\n", "    add_noise=True,\n", "    noise_std=0.01\n", ")\n", "\n", "print(\"Training data generation complete.\")\n", "print(f\"Source clouds shape: {source_clouds.shape}\")\n", "print(f\"Target clouds shape: {target_clouds.shape}\")\n", "print(f\"Rotations shape: {rotations_quat.shape}\")\n", "print(f\"Translations shape: {translations.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Split data into training and validation sets\n", "train_source, val_source, train_target, val_target, train_rot, val_rot, train_trans, val_trans = train_test_split(\n", "    source_clouds, target_clouds, rotations_quat, translations, test_size=0.2, random_state=42\n", ")\n", "\n", "print(f\"Training set size: {train_source.shape[0]} samples\")\n", "print(f\"Validation set size: {val_source.shape[0]} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create the neural network model\n", "model = create_point_cloud_alignment_model(num_points=num_points_per_cloud, use_quaternion=True)\n", "model.summary()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train the model\n", "train_data = (train_source, train_target, train_rot, train_trans)\n", "val_data = (val_source, val_target, val_rot, val_trans)\n", "\n", "history = train_model(\n", "    model=model,\n", "    train_data=train_data,\n", "    val_data=val_data,\n", "    epochs=20,  # Reduced for demonstration\n", "    batch_size=32,\n", "    learning_rate=0.001,\n", "    output_dir=output_dir\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot training history\n", "plot_training_history(history)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 7.1 Testing the Model\n", "\n", "Let's test our trained model on a new pair of point clouds."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a test case using real-world point clouds\n", "print(\"\\n=== Creating Test Case with Real-World Point Clouds ===\\n\")\n", "\n", "# Use the normalized IFC and LAS point clouds for testing\n", "# We'll subsample them to make the test faster\n", "max_test_points = 3000\n", "\n", "# Subsample the IFC point cloud for source\n", "if ifc_points_normalized.shape[0] > max_test_points:\n", "    indices = np.random.choice(ifc_points_normalized.shape[0], max_test_points, replace=False)\n", "    test_source = ifc_points_normalized[indices]\n", "else:\n", "    test_source = ifc_points_normalized\n", "\n", "# Apply a random transformation to create a target point cloud\n", "# This simulates the misalignment between the point clouds\n", "R_test, t_test, _, quat_test = generate_random_transformation(rotation_range=np.pi/6, translation_range=0.3)\n", "test_target = apply_transformation(test_source, R_test, t_test)\n", "\n", "print(f\"Test source shape: {test_source.shape}\")\n", "print(f\"Test target shape: {test_target.shape}\")\n", "print(f\"Ground truth rotation matrix:\\n{R_test}\")\n", "print(f\"Ground truth translation vector: {t_test}\")\n", "\n", "# Visualize the test point clouds\n", "if O3D_SUPPORT:\n", "    print(\"\\nVisualizing test point clouds...\")\n", "    \n", "    # Create Open3D point clouds\n", "    source_pcd = o3d.geometry.PointCloud()\n", "    source_pcd.points = o3d.utility.Vector3dVector(test_source)\n", "    source_pcd.paint_uniform_color([1, 0, 0])  # Red for source\n", "    \n", "    target_pcd = o3d.geometry.PointCloud()\n", "    target_pcd.points = o3d.utility.Vector3dVector(test_target)\n", "    target_pcd.paint_uniform_color([0, 1, 0])  # Green for target\n", "    \n", "    # Visualize\n", "    o3d.visualization.draw_geometries([source_pcd, target_pcd], window_name=\"Test Point Clouds\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create output directory for neural network results\n", "neural_output_dir = os.path.join(output_dir, 'neural_alignment')\n", "os.makedirs(neural_output_dir, exist_ok=True)\n", "print(f\"Output directory for neural network results: {neural_output_dir}\")\n", "\n", "# Evaluate the neural network method\n", "print(\"\\n=== Evaluating Neural Network Method ===\\n\")\n", "neural_start_time = time.time()\n", "aligned_source_neural, R_neural, t_neural = align_point_clouds_neural(\n", "    test_source, test_target, model, num_points=1024,\n", "    save_output=True, output_dir=neural_output_dir, output_format='las'\n", ")\n", "neural_time = time.time() - neural_start_time\n", "print(f\"Neural Network time: {neural_time:.4f} seconds\")\n", "print(f\"Neural Network rotation matrix:\\n{R_neural}\")\n", "print(f\"Neural Network translation vector: {t_neural}\")\n", "print(f\"Neural Network alignment results saved to: {neural_output_dir}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compute_rmse(source, target):\n", "    \"\"\"\n", "    Computes the Root Mean Square Error (RMSE) between two point clouds.\n", "    \n", "    Parameters:\n", "    -----------\n", "    source : numpy.ndarray\n", "        Source point cloud of shape (N, 3)\n", "    target : numpy.n<PERSON><PERSON>\n", "        Target point cloud of shape (M, 3)\n", "    \n", "    Returns:\n", "    --------\n", "    rmse : float\n", "        Root Mean Square Error\n", "    \"\"\"\n", "    # Find nearest neighbors\n", "    nn = NearestNeighbors(n_neighbors=1, algorithm='kd_tree').fit(target)\n", "    distances, _ = nn.kneighbors(source)\n", "    \n", "    # Compute RMSE\n", "    rmse = np.sqrt(np.mean(distances**2))\n", "    \n", "    return rmse"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compute RMSE for neural network method\n", "rmse_neural = compute_rmse(aligned_source_neural, test_target)\n", "\n", "print(\"\\n=== Neural Network Alignment Accuracy ===\\n\")\n", "print(f\"Neural Network RMSE: {rmse_neural:.6f}\")\n", "print(f\"Alignment time: {neural_time:.4f} seconds\")\n", "\n", "# Calculate error relative to ground truth transformation\n", "# Rotation error\n", "R_error = np.linalg.norm(R_neural - R_test, ord='fro')\n", "# Translation error\n", "t_error = np.linalg.norm(t_neural - t_test)\n", "\n", "print(f\"Rotation matrix error: {R_error:.6f}\")\n", "print(f\"Translation vector error: {t_error:.6f}\")\n", "\n", "# Visualize results\n", "plt.figure(figsize=(10, 5))\n", "\n", "# Plot point-to-point distances histogram\n", "nn = NearestNeighbors(n_neighbors=1, algorithm='kd_tree').fit(test_target)\n", "distances, _ = nn.kneighbors(aligned_source_neural)\n", "distances = distances.ravel()\n", "\n", "plt.hist(distances, bins=50, alpha=0.7)\n", "plt.axvline(x=rmse_neural, color='r', linestyle='--', label=f'RMSE: {rmse_neural:.6f}')\n", "plt.title('Point-to-Point Distances After Neural Network Alignment')\n", "plt.xlabel('Distance')\n", "plt.ylabel('Frequency')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define a visualization function that works with or without Open3D\n", "def visualize_point_clouds(source, target, aligned=None, use_matplotlib=True):\n", "    \"\"\"\n", "    Visualizes point clouds using either Open3D or Matplotlib.\n", "    \n", "    Parameters:\n", "    -----------\n", "    source : numpy.ndarray\n", "        Source point cloud of shape (N, 3)\n", "    target : numpy.n<PERSON><PERSON>\n", "        Target point cloud of shape (M, 3)\n", "    aligned : numpy.n<PERSON>ray, optional\n", "        Aligned source point cloud of shape (N, 3)\n", "    use_matplotlib : bool\n", "        Whether to use Matplotlib for visualization (fallback if Open3D is not available)\n", "    \"\"\"\n", "    # Try to use Open3D if available\n", "    if O3D_SUPPORT and not use_matplotlib:\n", "        # Create Open3D point clouds\n", "        source_pcd = o3d.geometry.PointCloud()\n", "        source_pcd.points = o3d.utility.Vector3dVector(source)\n", "        source_pcd.paint_uniform_color([1, 0, 0])  # Red for source\n", "        \n", "        target_pcd = o3d.geometry.PointCloud()\n", "        target_pcd.points = o3d.utility.Vector3dVector(target)\n", "        target_pcd.paint_uniform_color([0, 1, 0])  # Green for target\n", "        \n", "        if aligned is not None:\n", "            aligned_pcd = o3d.geometry.PointCloud()\n", "            aligned_pcd.points = o3d.utility.Vector3dVector(aligned)\n", "            aligned_pcd.paint_uniform_color([0, 0, 1])  # Blue for aligned\n", "            \n", "            # Visualize before and after alignment\n", "            print(\"Before alignment:\")\n", "            o3d.visualization.draw_geometries([source_pcd, target_pcd], window_name=\"Before Alignment\")\n", "            \n", "            print(\"After alignment:\")\n", "            o3d.visualization.draw_geometries([aligned_pcd, target_pcd], window_name=\"Alignment Result\")\n", "        else:\n", "            # Visualize source and target only\n", "            o3d.visualization.draw_geometries([source_pcd, target_pcd], window_name=\"Point Clouds\")\n", "    else:\n", "        # Fallback to <PERSON><PERSON><PERSON><PERSON>b\n", "        fig = plt.figure(figsize=(12, 5))\n", "        \n", "        if aligned is not None:\n", "            # Before alignment\n", "            ax1 = fig.add_subplot(121, projection='3d')\n", "            ax1.scatter(source[:, 0], source[:, 1], source[:, 2], c='r', s=1, alpha=0.5, label='Source')\n", "            ax1.scatter(target[:, 0], target[:, 1], target[:, 2], c='g', s=1, alpha=0.5, label='Target')\n", "            ax1.set_title('Before Alignment')\n", "            ax1.set_xlabel('X')\n", "            ax1.set_ylabel('Y')\n", "            ax1.set_zlabel('Z')\n", "            ax1.legend()\n", "            \n", "            # After alignment\n", "            ax2 = fig.add_subplot(122, projection='3d')\n", "            ax2.scatter(aligned[:, 0], aligned[:, 1], aligned[:, 2], c='b', s=1, alpha=0.5, label='Aligned Source')\n", "            ax2.scatter(target[:, 0], target[:, 1], target[:, 2], c='g', s=1, alpha=0.5, label='Target')\n", "            ax2.set_title('After Alignment')\n", "            ax2.set_xlabel('X')\n", "            ax2.set_ylabel('Y')\n", "            ax2.set_zlabel('Z')\n", "            ax2.legend()\n", "        else:\n", "            # Just source and target\n", "            ax = fig.add_subplot(111, projection='3d')\n", "            ax.scatter(source[:, 0], source[:, 1], source[:, 2], c='r', s=1, alpha=0.5, label='Source')\n", "            ax.scatter(target[:, 0], target[:, 1], target[:, 2], c='g', s=1, alpha=0.5, label='Target')\n", "            ax.set_title('Point Clouds')\n", "            ax.set_xlabel('X')\n", "            ax.set_ylabel('Y')\n", "            ax.set_zlabel('Z')\n", "            ax.legend()\n", "        \n", "        plt.tight_layout()\n", "        plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize the neural network alignment result\n", "print(\"\\n=== Visualizing Neural Network Alignment Result ===\\n\")\n", "try:\n", "    # Try to use the visualization function\n", "    visualize_point_clouds(test_source, test_target, aligned_source_neural)\n", "except Exception as e:\n", "    print(f\"Visualization error: {e}\")\n", "    print(\"Falling back to simple statistics:\")\n", "    print(f\"Source point cloud shape: {test_source.shape}\")\n", "    print(f\"Target point cloud shape: {test_target.shape}\")\n", "    print(f\"Aligned point cloud shape: {aligned_source_neural.shape}\")\n", "    print(f\"RMSE: {rmse_neural:.6f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. References\n", "\n", "1. <PERSON><PERSON><PERSON>, <PERSON><PERSON> & <PERSON>, <PERSON> (1992). A method for registration of 3-D shapes. IEEE Transactions on Pattern Analysis and Machine Intelligence, 14(2), 239-256.\n", "\n", "2. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, L. <PERSON> (2017). PointNet: Deep learning on point sets for 3D classification and segmentation. Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR).\n", "\n", "3. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, R. <PERSON>, & <PERSON>, S. (2019). PointNetLK: Robust & efficient point cloud registration using PointNet. Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR).\n", "\n", "4. <PERSON>, <PERSON>, & <PERSON>, <PERSON> (2019). Deep closest point: Learning representations for point cloud registration. Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV).\n", "\n", "5. <PERSON><PERSON><PERSON><PERSON>, <PERSON>, & <PERSON>, <PERSON> (2001). Efficient variants of the ICP algorithm. Proceedings Third International Conference on 3-D Digital Imaging and Modeling."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}