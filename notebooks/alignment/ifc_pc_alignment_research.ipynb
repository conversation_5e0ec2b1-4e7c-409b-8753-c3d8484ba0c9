{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IFC Point Cloud Alignment Research\n", "\n", "This notebook implements comprehensive IFC to point cloud alignment using various methods including:\n", "1. Traditional ICP (Iterative Closest Point)\n", "2. Neural network approaches\n", "3. Hybrid methods combining both\n", "\n", "**Author:** <PERSON><PERSON><PERSON>  \n", "**Date:** December 2024"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install tensorflow open3d matplotlib laspy transforms3d scipy pandas ifcopenshell ifcopenshell-geom ezdxf"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import tensorflow as tf\n", "import numpy as np\n", "import os\n", "import matplotlib.pyplot as plt\n", "import open3d as o3d\n", "import laspy\n", "import transforms3d.euler as t3d\n", "import logging\n", "import time\n", "from scipy.spatial import cKDTree\n", "import pandas as pd\n", "\n", "# Try to import ifcopenshell for IFC files\n", "try:\n", "    import ifcopenshell\n", "    import ifcopenshell.geom\n", "    IFC_SUPPORT = True\n", "except ImportError:\n", "    print(\"Warning: ifcopenshell not installed. IFC files will not be supported.\")\n", "    IFC_SUPPORT = False\n", "\n", "# Mount Google Drive to access data\n", "try:\n", "    from google.colab import drive\n", "    drive.mount('/content/gdrive')\n", "    IN_COLAB = True\n", "    print(\"Google Drive mounted successfully.\")\n", "except ImportError:\n", "    IN_COLAB = False\n", "    print(\"Not running in Google Colab. Using local file system.\")\n", "\n", "# Set random seeds for reproducibility\n", "np.random.seed(42)\n", "tf.random.set_seed(42)\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "print(f\"TensorFlow version: {tf.__version__}\")\n", "print(f\"NumPy version: {np.__version__}\")\n", "print(f\"Open3D version: {o3d.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_las_file(filename):\n", "    \"\"\"\n", "    Reads a LAS file and extracts point cloud data.\n", "    \"\"\"\n", "    logger.info(f\"Reading LAS file: {filename}\")\n", "    \n", "    try:\n", "        las_file = laspy.read(filename)\n", "        \n", "        # Extract coordinates\n", "        x = las_file.x\n", "        y = las_file.y\n", "        z = las_file.z\n", "        \n", "        # Combine into point cloud\n", "        points = np.vstack((x, y, z)).T\n", "        \n", "        logger.info(f\"Loaded {points.shape[0]} points from {filename}\")\n", "        return points\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error reading LAS file '{filename}': {e}\")\n", "        return None\n", "\n", "def read_ifc_file(filename):\n", "    \"\"\"\n", "    Reads an IFC file and extracts point cloud data.\n", "    \"\"\"\n", "    logger.info(f\"Reading IFC file: {filename}\")\n", "\n", "    if not IFC_SUPPORT:\n", "        logger.error(\"IFC support is not available. Please install ifcopenshell.\")\n", "        return None\n", "\n", "    try:\n", "        # Load IFC file\n", "        ifc_file = ifcopenshell.open(filename)\n", "        \n", "        # Create settings for geometry processing\n", "        settings = ifcopenshell.geom.settings()\n", "        settings.set(settings.USE_WORLD_COORDS, True)\n", "        \n", "        all_vertices = []\n", "        \n", "        # Extract geometry from all elements\n", "        for element in ifc_file.by_type('IfcProduct'):\n", "            if element.Representation:\n", "                try:\n", "                    shape = ifcopenshell.geom.create_shape(settings, element)\n", "                    vertices = shape.geometry.verts\n", "                    \n", "                    # Reshape vertices to (N, 3)\n", "                    vertices_array = np.array(vertices).reshape(-1, 3)\n", "                    all_vertices.append(vertices_array)\n", "                    \n", "                except Exception as e:\n", "                    logger.warning(f\"Could not process element {element.id()}: {e}\")\n", "                    continue\n", "\n", "        if all_vertices:\n", "            combined_vertices = np.vstack(all_vertices)\n", "            logger.info(f\"Loaded {combined_vertices.shape[0]} points from {filename}\")\n", "            return combined_vertices\n", "        else:\n", "            logger.error(f\"No geometry found in IFC file: {filename}\")\n", "            return None\n", "            \n", "    except Exception as e:\n", "        logger.error(f\"Error reading IFC file '{filename}': {e}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Point Cloud Processing Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def normalize_point_cloud(points):\n", "    \"\"\"\n", "    Normalizes a point cloud to be centered at the origin and scaled within a unit sphere.\n", "    \"\"\"\n", "    if points is None or len(points) == 0:\n", "        return None\n", "    \n", "    # Center the point cloud\n", "    centroid = np.mean(points, axis=0)\n", "    centered = points - centroid\n", "    \n", "    # Scale to unit sphere\n", "    max_distance = np.max(np.linalg.norm(centered, axis=1))\n", "    if max_distance > 0:\n", "        normalized = centered / max_distance\n", "    else:\n", "        normalized = centered\n", "    \n", "    return normalized\n", "\n", "def downsample_voxel(points, voxel_size=0.05):\n", "    \"\"\"\n", "    Downsample point cloud using voxel grid.\n", "    \"\"\"\n", "    if points is None or len(points) == 0:\n", "        return None\n", "    \n", "    # Create Open3D point cloud\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(points)\n", "    \n", "    # Downsample\n", "    downsampled = pcd.voxel_down_sample(voxel_size)\n", "    \n", "    return np.asarray(downsampled.points)\n", "\n", "def nearest_neighbor(source, target):\n", "    \"\"\"\n", "    Find nearest neighbors between source and target point clouds.\n", "    \"\"\"\n", "    tree = cKDTree(target)\n", "    distances, indices = tree.query(source)\n", "    return distances, indices"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. ICP Algorithm Implementation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def best_fit_transform(source, target):\n", "    \"\"\"\n", "    Calculates the least-squares best-fit transform between corresponding 3D points.\n", "    \"\"\"\n", "    assert source.shape == target.shape, \"Source and target point clouds must have the same shape\"\n", "\n", "    # Center both point clouds\n", "    source_centroid = np.mean(source, axis=0)\n", "    target_centroid = np.mean(target, axis=0)\n", "    source_centered = source - source_centroid\n", "    target_centered = target - target_centroid\n", "\n", "    # Compute covariance matrix H\n", "    H = np.dot(source_centered.T, target_centered)\n", "\n", "    # Singular Value Decomposition\n", "    U, S, Vt = np.linalg.svd(H)\n", "\n", "    # Compute rotation matrix R\n", "    R = np.dot(Vt.T, U.T)\n", "\n", "    # Ensure proper rotation (det(R) = 1)\n", "    if np.linalg.det(R) < 0:\n", "        Vt[-1, :] *= -1\n", "        R = np.dot(Vt.T, U.T)\n", "\n", "    # Compute translation\n", "    t = target_centroid - np.dot(R, source_centroid)\n", "\n", "    # Create homogeneous transformation matrix\n", "    T = np.identity(4)\n", "    T[:3, :3] = R\n", "    T[:3, 3] = t\n", "\n", "    return T, R, t\n", "\n", "def icp_algorithm(source, target, max_iterations=50, tolerance=1e-6, verbose=False):\n", "    \"\"\"\n", "    Iterative Closest Point (ICP) algorithm for point cloud alignment.\n", "    \"\"\"\n", "    # Make a copy of the source point cloud\n", "    source_copy = np.copy(source)\n", "    prev_error = 0\n", "\n", "    # Initialize transformation matrix\n", "    T_combined = np.identity(4)\n", "\n", "    start_time = time.time()\n", "\n", "    for iteration in range(max_iterations):\n", "        # Find nearest neighbors\n", "        distances, indices = nearest_neighbor(source_copy, target)\n", "\n", "        # Compute mean squared error\n", "        mean_error = np.mean(distances**2)\n", "\n", "        # Check for convergence\n", "        if verbose:\n", "            print(f\"Iteration {iteration+1}, MSE: {mean_error:.10f}\")\n", "\n", "        if abs(prev_error - mean_error) < tolerance:\n", "            if verbose:\n", "                print(f\"Converged after {iteration+1} iterations.\")\n", "            break\n", "\n", "        prev_error = mean_error\n", "\n", "        # Get corresponding points\n", "        corresponding_target_points = target[indices]\n", "\n", "        # Compute transformation\n", "        T, R, t = best_fit_transform(source_copy, corresponding_target_points)\n", "\n", "        # Update transformation matrix\n", "        T_combined = np.dot(T, T_combined)\n", "\n", "        # Apply transformation\n", "        source_copy = np.dot(source_copy, R.T) + t\n", "\n", "    end_time = time.time()\n", "\n", "    if verbose:\n", "        print(f\"ICP completed in {end_time - start_time:.4f} seconds\")\n", "        if iteration == max_iterations - 1:\n", "            print(f\"Warning: Maximum iterations ({max_iterations}) reached without convergence.\")\n", "\n", "    return T_combined, source_copy, mean_error, iteration + 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Visualization Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_point_cloud(points, title=\"Point Cloud\", point_size=1.0, color='blue'):\n", "    \"\"\"\n", "    Visualize a single point cloud using matplotlib.\n", "    \"\"\"\n", "    if points is None or len(points) == 0:\n", "        print(\"No points to visualize\")\n", "        return\n", "    \n", "    fig = plt.figure(figsize=(10, 8))\n", "    ax = fig.add_subplot(111, projection='3d')\n", "    \n", "    ax.scatter(points[:, 0], points[:, 1], points[:, 2], \n", "              c=color, s=point_size, alpha=0.6)\n", "    \n", "    ax.set_xlabel('X')\n", "    ax.set_ylabel('Y')\n", "    ax.set_zlabel('Z')\n", "    ax.set_title(title)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def visualize_point_clouds_comparison(source, target, aligned_source=None, title=\"Point Cloud Comparison\"):\n", "    \"\"\"\n", "    Visualize comparison between source, target, and optionally aligned source point clouds.\n", "    \"\"\"\n", "    try:\n", "        if aligned_source is not None:\n", "            fig = plt.figure(figsize=(18, 6))\n", "            \n", "            # Original source vs target\n", "            ax1 = fig.add_subplot(131, projection='3d')\n", "            ax1.scatter(source[:, 0], source[:, 1], source[:, 2], c='blue', s=1, alpha=0.6, label='Source')\n", "            ax1.scatter(target[:, 0], target[:, 1], target[:, 2], c='red', s=1, alpha=0.6, label='Target')\n", "            ax1.set_title('Before Alignment')\n", "            ax1.legend()\n", "            \n", "            # Aligned source vs target\n", "            ax2 = fig.add_subplot(132, projection='3d')\n", "            ax2.scatter(aligned_source[:, 0], aligned_source[:, 1], aligned_source[:, 2], c='green', s=1, alpha=0.6, label='Aligned Source')\n", "            ax2.scatter(target[:, 0], target[:, 1], target[:, 2], c='red', s=1, alpha=0.6, label='Target')\n", "            ax2.set_title('After Alignment')\n", "            ax2.legend()\n", "            \n", "            # Overlay comparison\n", "            ax3 = fig.add_subplot(133, projection='3d')\n", "            ax3.scatter(source[:, 0], source[:, 1], source[:, 2], c='blue', s=1, alpha=0.3, label='Original Source')\n", "            ax3.scatter(aligned_source[:, 0], aligned_source[:, 1], aligned_source[:, 2], c='green', s=1, alpha=0.6, label='Aligned Source')\n", "            ax3.scatter(target[:, 0], target[:, 1], target[:, 2], c='red', s=1, alpha=0.6, label='Target')\n", "            ax3.set_title('Overlay Comparison')\n", "            ax3.legend()\n", "        else:\n", "            fig = plt.figure(figsize=(10, 8))\n", "            ax = fig.add_subplot(111, projection='3d')\n", "            ax.scatter(source[:, 0], source[:, 1], source[:, 2], c='blue', s=1, alpha=0.6, label='Source')\n", "            ax.scatter(target[:, 0], target[:, 1], target[:, 2], c='red', s=1, alpha=0.6, label='Target')\n", "            ax.set_title('Point Cloud Comparison')\n", "            ax.legend()\n", "\n", "        plt.suptitle(title, fontsize=16)\n", "        plt.tight_layout()\n", "        plt.subplots_adjust(top=0.9)\n", "        plt.show()\n", "    except Exception as e:\n", "        print(\"Error occurred during comparison visualization:\", e)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Main Execution\n", "\n", "Load data and perform alignment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define paths to your files\n", "if IN_COLAB:\n", "    base_path = '/content/gdrive/MyDrive/pc-experiment'\n", "else:\n", "    base_path = 'data/pc-experiment'\n", "\n", "# File paths\n", "las_file = f\"{base_path}/Trino_Fly_2_Shifted.las\"\n", "ifc_file = f\"{base_path}/GRE.EEC.S.00.IT.P.14353.00.265.ifc\"\n", "\n", "print(f\"LAS file: {las_file}\")\n", "print(f\"IFC file: {ifc_file}\")\n", "\n", "# Load point cloud data\n", "try:\n", "    target_points = read_las_file(las_file)\n", "    print(f\"Loaded target point cloud: {target_points.shape if target_points is not None else 'Failed'}\")\n", "\n", "    if IFC_SUPPORT:\n", "        source_points = read_ifc_file(ifc_file)\n", "        print(f\"Loaded source point cloud: {source_points.shape if source_points is not None else 'Failed'}\")\n", "    else:\n", "        print(\"IFC support not available. Creating synthetic data.\")\n", "        source_points = None\n", "\n", "    # Create synthetic data if needed\n", "    if source_points is None or target_points is None:\n", "        print(\"Creating synthetic data...\")\n", "        if target_points is not None:\n", "            # Create synthetic source from target\n", "            angle = np.radians(15)\n", "            R = np.array([\n", "                [np.cos(angle), -np.sin(angle), 0],\n", "                [np.sin(angle), np.cos(angle), 0],\n", "                [0, 0, 1]\n", "            ])\n", "            t = np.array([0.5, 0.3, 0.2])\n", "            source_points = np.dot(target_points, R) - t\n", "        else:\n", "            # Create completely synthetic data\n", "            x, y, z = np.meshgrid(np.linspace(-1, 1, 10), np.linspace(-1, 1, 10), np.linspace(-1, 1, 10))\n", "            source_points = np.vstack((x.flatten(), y.flatten(), z.flatten())).T\n", "            \n", "            angle = np.radians(15)\n", "            R = np.array([\n", "                [np.cos(angle), -np.sin(angle), 0],\n", "                [np.sin(angle), np.cos(angle), 0],\n", "                [0, 0, 1]\n", "            ])\n", "            t = np.array([0.5, 0.3, 0.2])\n", "            target_points = np.dot(source_points, R.T) + t\n", "\n", "except Exception as e:\n", "    print(f\"Error loading point clouds: {e}\")\n", "    # Fallback synthetic data\n", "    source_points = np.random.rand(1000, 3)\n", "    target_points = source_points + 0.1 * np.random.rand(1000, 3)\n", "\n", "# Normalize and downsample\n", "source_pc = normalize_point_cloud(source_points)\n", "target_pc = normalize_point_cloud(target_points)\n", "\n", "print(f\"Source shape: {source_pc.shape}\")\n", "print(f\"Target shape: {target_pc.shape}\")\n", "\n", "# Downsample for faster processing\n", "voxel_size = 0.02 if source_pc.shape[0] > 10000 else 0.05\n", "subsampled_source_pc = downsample_voxel(source_pc, voxel_size=voxel_size)\n", "subsampled_target_pc = downsample_voxel(target_pc, voxel_size=voxel_size)\n", "\n", "print(f\"Downsampled Source shape: {subsampled_source_pc.shape}\")\n", "print(f\"Downsampled Target shape: {subsampled_target_pc.shape}\")\n", "\n", "# Visualize initial point clouds\n", "visualize_point_clouds_comparison(subsampled_source_pc, subsampled_target_pc,\n", "                                 title=\"Source vs Target Point Clouds (Before Alignment)\")\n", "\n", "# Run ICP algorithm\n", "T_icp, aligned_source_icp, icp_error, icp_iterations = icp_algorithm(\n", "    subsampled_source_pc, subsampled_target_pc, max_iterations=50, tolerance=1e-6, verbose=True)\n", "\n", "print(f\"\\nFinal ICP transformation matrix:\\n{T_icp}\")\n", "print(f\"Final mean squared error: {icp_error:.10f}\")\n", "\n", "# Visualize alignment result\n", "visualize_point_clouds_comparison(subsampled_source_pc, subsampled_target_pc, aligned_source_icp,\n", "                                 title=\"ICP Alignment Result\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}