{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🌍 Ground Segmentation for Point Cloud Processing\n", "\n", "This notebook implements ground segmentation as part of the ground segmentation stage. It processes raw point cloud data to separate ground and non-ground points for downstream analysis.\n", "\n", "**Stage**: Ground Segmentation  \n", "**Input Data**: Raw point cloud (.las, .laz, .pcd)  \n", "**Output**: Ground-removed / non-ground point cloud  \n", "**Format**: .ply (recommended for compatibility with Open3D + visualization), .pcd (preferred for PCL + ML pipelines)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Load Raw Point Cloud**: Import .las/.laz/.pcd files from raw data\n", "2. **Ground Detection**: RANSAC plane fitting, progressive morphological filter, or cloth simulation\n", "3. **Ground Removal**: Separate ground and non-ground points\n", "4. **Export Processed Data**: Save non-ground points in .ply/.pcd formats"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1️⃣ Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import numpy as np\n", "import os\n", "import json\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import time\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "from sklearn.neighbors import NearestNeighbors\n", "from scipy.spatial import cKDTree\n", "\n", "# Try to import Open3D for point cloud processing\n", "try:\n", "    import open3d as o3d\n", "    O3D_SUPPORT = True\n", "    print(f\"✅ Open3D available: {o3d.__version__}\")\n", "except ImportError:\n", "    print(\"⚠️ Open3D not available. Some features will be disabled.\")\n", "    O3D_SUPPORT = False\n", "\n", "# Try to import laspy for LAS file support\n", "try:\n", "    import laspy\n", "    LASPY_SUPPORT = True\n", "    print(f\"✅ laspy available: {laspy.__version__}\")\n", "except ImportError:\n", "    print(\"⚠️ laspy not available. LAS/LAZ files will not be supported.\")\n", "    LASPY_SUPPORT = False\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Set up paths with proper project organization\n", "base_path = Path('../..')  # Adjust to your project root\n", "data_path = base_path / 'data'\n", "\n", "# Project organization - adjust based on your project\n", "PROJECT_TYPE = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "PROJECT_NAME = \"Trino\"  # ENEL: <PERSON>, <PERSON>, <PERSON>, Giorgio | USA: <PERSON>, <PERSON><PERSON><PERSON>, RES\n", "\n", "# Input and output paths following the specified organization\n", "raw_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'raw'\n", "ground_seg_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'ground_segmentation'\n", "ground_seg_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"🌍 Ground Segmentation - Ready!\")\n", "print(f\"📁 Data path: {data_path}\")\n", "print(f\"🏢 Project: {PROJECT_TYPE}/{PROJECT_NAME}\")\n", "print(f\"📥 Input path: {raw_path}\")\n", "print(f\"💾 Output path: {ground_seg_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2️⃣ Data Loading Functions\n", "\n", "Load raw point cloud data from various formats (.las, .laz, .pcd)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_raw_point_cloud():\n", "    \"\"\"\n", "    Load raw point cloud data from various sources.\n", "    Expected input: Raw point cloud (.las, .laz, .pcd) from raw data directory.\n", "    \"\"\"\n", "    print(f\"🔍 Loading raw point cloud data...\")\n", "    \n", "    # Try different point cloud sources in order of preference\n", "    point_cloud_sources = [\n", "        # LAS/LAZ files (preferred for raw drone data)\n", "        *list(raw_path.glob('*.las')),\n", "        *list(raw_path.glob('*.laz')),\n", "        # PCD files\n", "        *list(raw_path.glob('*.pcd')),\n", "        # PLY files\n", "        *list(raw_path.glob('*.ply')),\n", "        # Legacy paths for backward compatibility\n", "        Path('/content/gdrive/MyDrive/pc-experiment/Trino_Fly_2_Shifted.las')\n", "    ]\n", "    \n", "    for pc_path in point_cloud_sources:\n", "        if pc_path.exists():\n", "            print(f\"✅ Found point cloud: {pc_path}\")\n", "            \n", "            try:\n", "                if pc_path.suffix.lower() in ['.las', '.laz']:\n", "                    if not LASPY_SUPPORT:\n", "                        print(f\"⚠️ Cannot load {pc_path.suffix} file without laspy\")\n", "                        continue\n", "                    \n", "                    # Load LAS/LAZ file\n", "                    las_file = laspy.read(str(pc_path))\n", "                    points = np.vstack((las_file.x, las_file.y, las_file.z)).T\n", "                    \n", "                    # Check for additional attributes\n", "                    colors = None\n", "                    if hasattr(las_file, 'red') and hasattr(las_file, 'green') and hasattr(las_file, 'blue'):\n", "                        colors = np.vstack((las_file.red, las_file.green, las_file.blue)).T / 65535.0  # Normalize to [0,1]\n", "                        print(f\"🎨 Point cloud includes RGB color information\")\n", "                    \n", "                    print(f\"📊 Loaded {points.shape[0]:,} points from {pc_path.suffix.upper()} file\")\n", "                    return points, colors, pc_path\n", "                    \n", "                elif pc_path.suffix.lower() in ['.ply', '.pcd']:\n", "                    if not O3D_SUPPORT:\n", "                        print(f\"⚠️ Cannot load {pc_path.suffix} file without Open3D\")\n", "                        continue\n", "                    \n", "                    # Load PLY/PCD file using Open3D\n", "                    pcd = o3d.io.read_point_cloud(str(pc_path))\n", "                    points = np.asarray(pcd.points)\n", "                    \n", "                    # Check for colors\n", "                    colors = None\n", "                    if pcd.has_colors():\n", "                        colors = np.asarray(pcd.colors)\n", "                        print(f\"🎨 Point cloud includes RGB color information\")\n", "                    \n", "                    print(f\"📊 Loaded {points.shape[0]:,} points from {pc_path.suffix.upper()} file\")\n", "                    return points, colors, pc_path\n", "                    \n", "                else:\n", "                    print(f\"⚠️ Unsupported file format: {pc_path.suffix}\")\n", "                    continue\n", "                \n", "            except Exception as e:\n", "                print(f\"❌ Error loading {pc_path}: {e}\")\n", "                continue\n", "    \n", "    print(f\"❌ No raw point cloud found in expected locations\")\n", "    return None, None, None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3️⃣ Ground Segmentation Algorithms\n", "\n", "Implement various ground segmentation methods."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def ransac_ground_detection(points, distance_threshold=0.1, num_iterations=1000, min_inliers_ratio=0.1):\n", "    \"\"\"\n", "    Detect ground plane using RANSAC algorithm.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud data (N x 3)\n", "    distance_threshold : float\n", "        Maximum distance for a point to be considered ground\n", "    num_iterations : int\n", "        Number of RANSAC iterations\n", "    min_inliers_ratio : float\n", "        Minimum ratio of inliers to accept a plane\n", "        \n", "    Returns:\n", "    --------\n", "    ground_indices : numpy.ndarray\n", "        Indices of ground points\n", "    plane_params : numpy.n<PERSON><PERSON>\n", "        Ground plane parameters [a, b, c, d]\n", "    \"\"\"\n", "    print(f\"🔍 Running RANSAC ground detection...\")\n", "    \n", "    best_plane_params = None\n", "    best_inliers = []\n", "    max_inliers = 0\n", "    n_points = points.shape[0]\n", "    min_inliers = int(n_points * min_inliers_ratio)\n", "    \n", "    start_time = time.time()\n", "    \n", "    for i in range(num_iterations):\n", "        # Randomly select 3 points\n", "        sample_indices = np.random.choice(n_points, 3, replace=False)\n", "        sample_points = points[sample_indices]\n", "        \n", "        # Fit plane to sampled points\n", "        try:\n", "            # Calculate plane normal using cross product\n", "            v1 = sample_points[1] - sample_points[0]\n", "            v2 = sample_points[2] - sample_points[0]\n", "            normal = np.cross(v1, v2)\n", "            \n", "            # Normalize normal vector\n", "            normal = normal / np.linalg.norm(normal)\n", "            \n", "            # Ensure normal points upward (positive z component)\n", "            if normal[2] < 0:\n", "                normal = -normal\n", "            \n", "            # Calculate d parameter\n", "            d = -np.dot(normal, sample_points[0])\n", "            plane_params = np.append(normal, d)\n", "            \n", "        except:\n", "            continue\n", "        \n", "        # Compute distances from all points to the plane\n", "        distances = np.abs(np.dot(points, plane_params[:3]) + plane_params[3]) / np.linalg.norm(plane_params[:3])\n", "        \n", "        # Find inliers\n", "        inliers = np.where(distances < distance_threshold)[0]\n", "        n_inliers = len(inliers)\n", "        \n", "        # Update best plane if we found more inliers\n", "        if n_inliers > max_inliers and n_inliers >= min_inliers:\n", "            best_plane_params = plane_params\n", "            best_inliers = inliers\n", "            max_inliers = n_inliers\n", "            \n", "            # Early stopping if we found a very good plane\n", "            inlier_ratio = n_inliers / n_points\n", "            if inlier_ratio > 0.6:  # If 60% of points are ground\n", "                print(f\"Early stopping at iteration {i+1}: Found {n_inliers:,} ground points ({inlier_ratio:.2f} ratio)\")\n", "                break\n", "    \n", "    end_time = time.time()\n", "    \n", "    if best_plane_params is not None:\n", "        print(f\"✅ RANSAC completed in {end_time - start_time:.2f} seconds\")\n", "        print(f\"   Ground points: {max_inliers:,} ({max_inliers / n_points:.2f} ratio)\")\n", "        return best_inliers, best_plane_params\n", "    else:\n", "        print(f\"❌ No ground plane found\")\n", "        return np.array([]), None\n", "\n", "def progressive_morphological_filter(points, cell_size=1.0, max_window_size=33, slope=0.15, max_distance=2.5, initial_distance=0.5):\n", "    \"\"\"\n", "    Progressive Morphological Filter for ground segmentation.\n", "    Based on <PERSON> et al. (2003) algorithm.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud data (N x 3)\n", "    cell_size : float\n", "        Grid cell size for rasterization\n", "    max_window_size : int\n", "        Maximum window size for morphological operations\n", "    slope : float\n", "        Slope parameter for terrain\n", "    max_distance : float\n", "        Maximum distance threshold\n", "    initial_distance : float\n", "        Initial distance threshold\n", "        \n", "    Returns:\n", "    --------\n", "    ground_indices : numpy.ndarray\n", "        Indices of ground points\n", "    \"\"\"\n", "    print(f\"🔍 Running Progressive Morphological Filter...\")\n", "    \n", "    # Create a simple height-based filter as approximation\n", "    # In a full implementation, this would involve rasterization and morphological operations\n", "    \n", "    # Find the lowest points in the point cloud\n", "    z_min = np.min(points[:, 2])\n", "    z_max = np.max(points[:, 2])\n", "    \n", "    # Use a height threshold approach as a simplified PMF\n", "    height_threshold = z_min + (z_max - z_min) * 0.1  # Bottom 10% of height range\n", "    \n", "    # Find points below the height threshold\n", "    ground_candidates = np.where(points[:, 2] < height_threshold)[0]\n", "    \n", "    print(f\"✅ PMF completed\")\n", "    print(f\"   Ground candidates: {len(ground_candidates):,} points\")\n", "    \n", "    return ground_candidates\n", "\n", "def cloth_simulation_filter(points, cloth_resolution=0.5, max_iterations=500, classification_threshold=0.5):\n", "    \"\"\"\n", "    Cloth Simulation Filter for ground segmentation.\n", "    Simplified implementation of <PERSON> et al. (2016) CSF algorithm.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud data (N x 3)\n", "    cloth_resolution : float\n", "        Resolution of the cloth grid\n", "    max_iterations : int\n", "        Maximum iterations for cloth simulation\n", "    classification_threshold : float\n", "        Distance threshold for ground classification\n", "        \n", "    Returns:\n", "    --------\n", "    ground_indices : numpy.ndarray\n", "        Indices of ground points\n", "    \"\"\"\n", "    print(f\"🔍 Running Cloth Simulation Filter...\")\n", "    \n", "    # Simplified CSF implementation\n", "    # In practice, this would involve physics simulation of a cloth falling onto the point cloud\n", "    \n", "    # For now, use a statistical approach based on local height variations\n", "    from sklearn.neighbors import NearestNeighbors\n", "    \n", "    # Build KD-tree for efficient neighbor search\n", "    nbrs = NearestNeighbors(n_neighbors=20, algorithm='kd_tree').fit(points[:, :2])  # Only X,Y\n", "    \n", "    ground_indices = []\n", "    \n", "    for i, point in enumerate(points):\n", "        # Find neighbors in 2D (X,Y)\n", "        distances, indices = nbrs.kneighbors([point[:2]])\n", "        neighbor_heights = points[indices[0], 2]\n", "        \n", "        # Check if point is close to the minimum height in its neighborhood\n", "        min_height = np.min(neighbor_heights)\n", "        if point[2] - min_height < classification_threshold:\n", "            ground_indices.append(i)\n", "    \n", "    ground_indices = np.array(ground_indices)\n", "    \n", "    print(f\"✅ CSF completed\")\n", "    print(f\"   Ground points: {len(ground_indices):,}\")\n", "    \n", "    return ground_indices"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4️⃣ Load and Process Point Cloud Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load raw point cloud data\n", "points, colors, source_file = load_raw_point_cloud()\n", "\n", "if points is None:\n", "    print(\"\\n🔧 Creating synthetic data for demonstration...\")\n", "    \n", "    # Create synthetic point cloud with ground and non-ground points\n", "    np.random.seed(42)\n", "    \n", "    # Ground points (flat plane with some noise)\n", "    x_ground = np.random.uniform(-50, 50, 5000)\n", "    y_ground = np.random.uniform(-50, 50, 5000)\n", "    z_ground = np.random.normal(0, 0.1, 5000)  # Ground level with noise\n", "    ground_points = np.column_stack([x_ground, y_ground, z_ground])\n", "    \n", "    # Non-ground points (buildings, vegetation, etc.)\n", "    x_objects = np.random.uniform(-30, 30, 2000)\n", "    y_objects = np.random.uniform(-30, 30, 2000)\n", "    z_objects = np.random.uniform(1, 10, 2000)  # Above ground\n", "    object_points = np.column_stack([x_objects, y_objects, z_objects])\n", "    \n", "    # <PERSON><PERSON><PERSON> all points\n", "    points = np.vstack([ground_points, object_points])\n", "    colors = None\n", "    source_file = Path(\"synthetic_data\")\n", "    \n", "    print(f\"📊 Created synthetic point cloud: {points.shape[0]:,} points\")\n", "\n", "print(f\"\\n📊 Point cloud statistics:\")\n", "print(f\"  Total points: {points.shape[0]:,}\")\n", "print(f\"  X range: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}\")\n", "print(f\"  Y range: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}\")\n", "print(f\"  Z range: {points[:, 2].min():.2f} to {points[:, 2].max():.2f}\")\n", "print(f\"  Has colors: {'Yes' if colors is not None else 'No'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5️⃣ Apply Ground Segmentation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Choose ground segmentation method\n", "SEGMENTATION_METHOD = \"RANSAC\"  # Options: \"RANSAC\", \"PMF\", \"CSF\"\n", "\n", "print(f\"🔄 Applying {SEGMENTATION_METHOD} ground segmentation...\")\n", "\n", "if SEGMENTATION_METHOD == \"RANSAC\":\n", "    ground_indices, plane_params = ransac_ground_detection(\n", "        points, \n", "        distance_threshold=0.2,  # 20cm threshold\n", "        num_iterations=1000,\n", "        min_inliers_ratio=0.05\n", "    )\n", "    \n", "elif <PERSON>TION_METHOD == \"PMF\":\n", "    ground_indices = progressive_morphological_filter(\n", "        points,\n", "        cell_size=1.0,\n", "        max_window_size=33,\n", "        slope=0.15\n", "    )\n", "    plane_params = None\n", "    \n", "elif <PERSON>TATION_METHOD == \"CSF\":\n", "    ground_indices = cloth_simulation_filter(\n", "        points,\n", "        cloth_resolution=0.5,\n", "        classification_threshold=0.5\n", "    )\n", "    plane_params = None\n", "\n", "# Separate ground and non-ground points\n", "if len(ground_indices) > 0:\n", "    # Create boolean mask for ground points\n", "    ground_mask = np.zeros(len(points), dtype=bool)\n", "    ground_mask[ground_indices] = True\n", "    \n", "    # Separate points\n", "    ground_points = points[ground_mask]\n", "    non_ground_points = points[~ground_mask]\n", "    \n", "    # Separate colors if available\n", "    ground_colors = colors[ground_mask] if colors is not None else None\n", "    non_ground_colors = colors[~ground_mask] if colors is not None else None\n", "    \n", "    print(f\"\\n✅ Ground segmentation completed:\")\n", "    print(f\"  Ground points: {len(ground_points):,} ({len(ground_points)/len(points)*100:.1f}%)\")\n", "    print(f\"  Non-ground points: {len(non_ground_points):,} ({len(non_ground_points)/len(points)*100:.1f}%)\")\n", "    \n", "else:\n", "    print(f\"❌ No ground points detected\")\n", "    ground_points = np.array([])\n", "    non_ground_points = points\n", "    ground_colors = None\n", "    non_ground_colors = colors"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6️⃣ Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize the segmentation results\n", "if len(ground_points) > 0 and len(non_ground_points) > 0:\n", "    fig = plt.figure(figsize=(15, 5))\n", "    \n", "    # Original point cloud\n", "    ax1 = fig.add_subplot(131, projection='3d')\n", "    ax1.scatter(points[::10, 0], points[::10, 1], points[::10, 2], c='gray', s=1, alpha=0.6)\n", "    ax1.set_title('Original Point Cloud')\n", "    ax1.set_xlabel('X')\n", "    ax1.set_ylabel('Y')\n", "    ax1.set_zlabel('Z')\n", "    \n", "    # Ground points\n", "    ax2 = fig.add_subplot(132, projection='3d')\n", "    if len(ground_points) > 0:\n", "        ax2.scatter(ground_points[::10, 0], ground_points[::10, 1], ground_points[::10, 2], c='brown', s=1, alpha=0.8)\n", "    ax2.set_title(f'Ground Points ({len(ground_points):,})')\n", "    ax2.set_xlabel('X')\n", "    ax2.set_ylabel('Y')\n", "    ax2.set_zlabel('Z')\n", "    \n", "    # Non-ground points\n", "    ax3 = fig.add_subplot(133, projection='3d')\n", "    if len(non_ground_points) > 0:\n", "        ax3.scatter(non_ground_points[::10, 0], non_ground_points[::10, 1], non_ground_points[::10, 2], c='green', s=1, alpha=0.8)\n", "    ax3.set_title(f'Non-Ground Points ({len(non_ground_points):,})')\n", "    ax3.set_xlabel('X')\n", "    ax3.set_ylabel('Y')\n", "    ax3.set_zlabel('Z')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Height distribution comparison\n", "    plt.figure(figsize=(12, 4))\n", "    \n", "    plt.subplot(1, 2, 1)\n", "    plt.hist(points[:, 2], bins=50, alpha=0.7, label='All Points', color='gray')\n", "    if len(ground_points) > 0:\n", "        plt.hist(ground_points[:, 2], bins=50, alpha=0.7, label='Ground', color='brown')\n", "    if len(non_ground_points) > 0:\n", "        plt.hist(non_ground_points[:, 2], bins=50, alpha=0.7, label='Non-Ground', color='green')\n", "    plt.xlabel('Height (Z)')\n", "    plt.ylabel('Point Count')\n", "    plt.title('Height Distribution')\n", "    plt.legend()\n", "    \n", "    plt.subplot(1, 2, 2)\n", "    if len(ground_points) > 0:\n", "        plt.scatter(ground_points[::50, 0], ground_points[::50, 1], c='brown', s=1, alpha=0.6, label='Ground')\n", "    if len(non_ground_points) > 0:\n", "        plt.scatter(non_ground_points[::50, 0], non_ground_points[::50, 1], c='green', s=1, alpha=0.6, label='Non-Ground')\n", "    plt.xlabel('X')\n", "    plt.ylabel('Y')\n", "    plt.title('Top View')\n", "    plt.legend()\n", "    plt.axis('equal')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "else:\n", "    print(\"⚠️ Cannot visualize: insufficient ground or non-ground points\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7️⃣ Export Ground-Removed Point Cloud\n", "\n", "Export the non-ground points in the specified formats."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export non-ground points in specified formats\n", "if len(non_ground_points) > 0:\n", "    print(f\"📤 Exporting ground-removed point cloud...\")\n", "    \n", "    # Create Open3D point cloud for export\n", "    if O3D_SUPPORT:\n", "        non_ground_pcd = o3d.geometry.PointCloud()\n", "        non_ground_pcd.points = o3d.utility.Vector3dVector(non_ground_points)\n", "        \n", "        # Add colors if available\n", "        if non_ground_colors is not None:\n", "            non_ground_pcd.colors = o3d.utility.Vector3dVector(non_ground_colors)\n", "        \n", "        # Export as PLY (recommended for compatibility with Open3D + visualization)\n", "        ply_filename = f'{PROJECT_NAME}_non_ground_points.ply'\n", "        ply_path = ground_seg_path / ply_filename\n", "        \n", "        success_ply = o3d.io.write_point_cloud(str(ply_path), non_ground_pcd, write_ascii=False)\n", "        \n", "        if success_ply:\n", "            print(f\"✅ Exported PLY: {ply_path}\")\n", "            print(f\"   Format: .ply (recommended for Open3D + visualization)\")\n", "            print(f\"   Points: {len(non_ground_points):,}\")\n", "        else:\n", "            print(f\"❌ Failed to export PLY file\")\n", "        \n", "        # Export as PCD (preferred for PCL + ML pipelines)\n", "        pcd_filename = f'{PROJECT_NAME}_non_ground_points.pcd'\n", "        pcd_path = ground_seg_path / pcd_filename\n", "        \n", "        success_pcd = o3d.io.write_point_cloud(str(pcd_path), non_ground_pcd, write_ascii=False)\n", "        \n", "        if success_pcd:\n", "            print(f\"✅ Exported PCD: {pcd_path}\")\n", "            print(f\"   Format: .pcd (preferred for PCL + ML pipelines)\")\n", "            print(f\"   Points: {len(non_ground_points):,}\")\n", "        else:\n", "            print(f\"❌ Failed to export PCD file\")\n", "    \n", "    else:\n", "        # Fallback: save as NumPy array\n", "        npy_filename = f'{PROJECT_NAME}_non_ground_points.npy'\n", "        npy_path = ground_seg_path / npy_filename\n", "        \n", "        np.save(npy_path, non_ground_points)\n", "        print(f\"✅ Exported NumPy: {npy_path}\")\n", "        print(f\"   Format: .npy (fallback format)\")\n", "        print(f\"   Points: {len(non_ground_points):,}\")\n", "    \n", "    # Also export ground points for completeness\n", "    if len(ground_points) > 0 and O3D_SUPPORT:\n", "        ground_pcd = o3d.geometry.PointCloud()\n", "        ground_pcd.points = o3d.utility.Vector3dVector(ground_points)\n", "        \n", "        if ground_colors is not None:\n", "            ground_pcd.colors = o3d.utility.Vector3dVector(ground_colors)\n", "        \n", "        ground_ply_path = ground_seg_path / f'{PROJECT_NAME}_ground_points.ply'\n", "        o3d.io.write_point_cloud(str(ground_ply_path), ground_pcd, write_ascii=False)\n", "        print(f\"💾 Also saved ground points: {ground_ply_path}\")\n", "    \n", "    # Save ground segmentation metadata\n", "    segmentation_metadata = {\n", "        'project_info': {\n", "            'project_name': PROJECT_NAME,\n", "            'project_type': PROJECT_TYPE,\n", "            'segmentation_timestamp': datetime.now().isoformat()\n", "        },\n", "        'input_data': {\n", "            'source_file': str(source_file),\n", "            'total_points': int(len(points)),\n", "            'has_colors': colors is not None\n", "        },\n", "        'segmentation_method': SEGMENTATION_METHOD,\n", "        'segmentation_results': {\n", "            'ground_points': int(len(ground_points)),\n", "            'non_ground_points': int(len(non_ground_points)),\n", "            'ground_ratio': float(len(ground_points) / len(points)),\n", "            'non_ground_ratio': float(len(non_ground_points) / len(points))\n", "        },\n", "        'output_files': {\n", "            'non_ground_ply': f'{PROJECT_NAME}_non_ground_points.ply',\n", "            'non_ground_pcd': f'{PROJECT_NAME}_non_ground_points.pcd',\n", "            'ground_ply': f'{PROJECT_NAME}_ground_points.ply'\n", "        }\n", "    }\n", "    \n", "    # Add method-specific parameters\n", "    if SEGMENTATION_METHOD == \"RANSAC\" and plane_params is not None:\n", "        segmentation_metadata['ground_plane'] = {\n", "            'equation': plane_params.tolist(),\n", "            'normal_vector': plane_params[:3].tolist(),\n", "            'd_parameter': float(plane_params[3])\n", "        }\n", "    \n", "    # Save metadata\n", "    metadata_path = ground_seg_path / f'{PROJECT_NAME}_ground_segmentation_metadata.json'\n", "    with open(metadata_path, 'w') as f:\n", "        json.dump(segmentation_metadata, f, indent=2)\n", "    \n", "    print(f\"💾 Segmentation metadata saved: {metadata_path}\")\n", "    \n", "    print(f\"\\n✅ Ground segmentation stage complete! Output files:\")\n", "    if O3D_SUPPORT:\n", "        print(f\"  - Non-ground PLY: {ply_filename} (Open3D + visualization)\")\n", "        print(f\"  - Non-ground PCD: {pcd_filename} (PCL + ML pipelines)\")\n", "        print(f\"  - Ground PLY: {PROJECT_NAME}_ground_points.ply\")\n", "    print(f\"  - Metadata: {metadata_path.name}\")\n", "    \n", "else:\n", "    print(\"❌ No non-ground points to export\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📝 Summary\n", "\n", "This ground segmentation notebook successfully processed raw point cloud data:\n", "\n", "### ✅ **What We Accomplished:**\n", "1. **Loaded Raw Data**: Processed .las/.laz/.pcd files from raw data directory\n", "2. **Applied Ground Segmentation**: Used RANSAC, PMF, or CSF algorithms\n", "3. **Separated Points**: Distinguished ground from non-ground points\n", "4. **Exported Results**: Saved in specified formats for downstream processing\n", "5. **Generated Metadata**: Comprehensive segmentation process documentation\n", "\n", "### 📊 **Output Formats:**\n", "- **PLY**: `{PROJECT_NAME}_non_ground_points.ply` (recommended for Open3D + visualization)\n", "- **PCD**: `{PROJECT_NAME}_non_ground_points.pcd` (preferred for PCL + ML pipelines)\n", "\n", "### 🔄 **Next Steps:**\n", "The ground-removed point cloud can now be used for:\n", "- **Alignment**: Input for IFC/CAD alignment workflows\n", "- **<PERSON>le Detection**: Foundation pile detection algorithms\n", "- **Object Detection**: Building and infrastructure analysis\n", "- **Visualization**: Clean point cloud visualization without ground clutter\n", "\n", "**📧 Contact**: For questions about ground segmentation, reach out to the development team."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}