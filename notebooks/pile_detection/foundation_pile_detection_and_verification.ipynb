{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Pre-Construction Quality Assessment for Solar Infrastructure\n", "## 4. Foundation Pile Detection and Verification"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we'll implement functions to detect pile foundations in the point cloud and verify their positions against the CAD design."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting tensorflow\n", "  Downloading tensorflow-2.19.0-cp310-cp310-macosx_12_0_arm64.whl.metadata (4.0 kB)\n", "Collecting open3d\n", "  Downloading open3d-0.19.0-cp310-cp310-macosx_10_15_universal2.whl.metadata (4.2 kB)\n", "Requirement already satisfied: matplotlib in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (3.9.2)\n", "Collecting laspy\n", "  Downloading laspy-2.5.4-py3-none-any.whl.metadata (3.5 kB)\n", "Collecting transforms3d\n", "  Downloading transforms3d-0.4.2-py3-none-any.whl.metadata (2.8 kB)\n", "Requirement already satisfied: scipy in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (1.14.1)\n", "Requirement already satisfied: pandas in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (2.1.3)\n", "Collecting absl-py>=1.0.0 (from tensorflow)\n", "  Downloading absl_py-2.2.2-py3-none-any.whl.metadata (2.6 kB)\n", "Collecting astunparse>=1.6.0 (from tensorflow)\n", "  Using cached astunparse-1.6.3-py2.py3-none-any.whl.metadata (4.4 kB)\n", "Collecting flatbuffers>=24.3.25 (from tensorflow)\n", "  Using cached flatbuffers-25.2.10-py2.py3-none-any.whl.metadata (875 bytes)\n", "Collecting gast!=0.5.0,!=0.5.1,!=0.5.2,>=0.2.1 (from tensorflow)\n", "  Using cached gast-0.6.0-py3-none-any.whl.metadata (1.3 kB)\n", "Collecting google-pasta>=0.1.1 (from tensorflow)\n", "  Using cached google_pasta-0.2.0-py3-none-any.whl.metadata (814 bytes)\n", "Collecting libclang>=13.0.0 (from tensorflow)\n", "  Using cached libclang-18.1.1-1-py2.py3-none-macosx_11_0_arm64.whl.metadata (5.2 kB)\n", "Collecting opt-einsum>=2.3.2 (from tensorflow)\n", "  Using cached opt_einsum-3.4.0-py3-none-any.whl.metadata (6.3 kB)\n", "Requirement already satisfied: packaging in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from tensorflow) (24.2)\n", "Collecting protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<6.0.0dev,>=3.20.3 (from tensorflow)\n", "  Using cached protobuf-5.29.4-cp38-abi3-macosx_10_9_universal2.whl.metadata (592 bytes)\n", "Requirement already satisfied: requests<3,>=2.21.0 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from tensorflow) (2.32.3)\n", "Requirement already satisfied: setuptools in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from tensorflow) (75.1.0)\n", "Requirement already satisfied: six>=1.12.0 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from tensorflow) (1.16.0)\n", "Collecting termcolor>=1.1.0 (from tensorflow)\n", "  Downloading termcolor-3.1.0-py3-none-any.whl.metadata (6.4 kB)\n", "Requirement already satisfied: typing-extensions>=3.6.6 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from tensorflow) (4.12.2)\n", "Collecting wrapt>=1.11.0 (from tensorflow)\n", "  Downloading wrapt-1.17.2-cp310-cp310-macosx_11_0_arm64.whl.metadata (6.4 kB)\n", "Collecting grpcio<2.0,>=1.24.3 (from tensorflow)\n", "  Downloading grpcio-1.71.0-cp310-cp310-macosx_12_0_universal2.whl.metadata (3.8 kB)\n", "Collecting tensorboard~=2.19.0 (from tensorflow)\n", "  Downloading tensorboard-2.19.0-py3-none-any.whl.metadata (1.8 kB)\n", "Collecting keras>=3.5.0 (from tensorflow)\n", "  Downloading keras-3.10.0-py3-none-any.whl.metadata (6.0 kB)\n", "Requirement already satisfied: numpy<2.2.0,>=1.26.0 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from tensorflow) (1.26.2)\n", "Collecting h5py>=3.11.0 (from tensorflow)\n", "  Downloading h5py-3.13.0-cp310-cp310-macosx_11_0_arm64.whl.metadata (2.5 kB)\n", "Collecting ml-dtypes<1.0.0,>=0.5.1 (from tensorflow)\n", "  Downloading ml_dtypes-0.5.1-cp310-cp310-macosx_10_9_universal2.whl.metadata (21 kB)\n", "Collecting tensorflow-io-gcs-filesystem>=0.23.1 (from tensorflow)\n", "  Downloading tensorflow_io_gcs_filesystem-0.37.1-cp310-cp310-macosx_12_0_arm64.whl.metadata (14 kB)\n", "Collecting dash>=2.6.0 (from open3d)\n", "  Downloading dash-3.0.4-py3-none-any.whl.metadata (10 kB)\n", "Collecting werkzeug>=3.0.0 (from open3d)\n", "  Using cached werkzeug-3.1.3-py3-none-any.whl.metadata (3.7 kB)\n", "Collecting flask>=3.0.0 (from open3d)\n", "  Downloading flask-3.1.1-py3-none-any.whl.metadata (3.0 kB)\n", "Collecting nbformat>=5.7.0 (from open3d)\n", "  Using cached nbformat-5.10.4-py3-none-any.whl.metadata (3.6 kB)\n", "Collecting configargparse (from open3d)\n", "  Using cached ConfigArgParse-1.7-py3-none-any.whl.metadata (23 kB)\n", "Collecting addict (from open3d)\n", "  Downloading addict-2.4.0-py3-none-any.whl.metadata (1.0 kB)\n", "Requirement already satisfied: pillow>=9.3.0 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from open3d) (10.3.0)\n", "Requirement already satisfied: pyyaml>=5.4.1 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from open3d) (6.0.2)\n", "Requirement already satisfied: scikit-learn>=0.21 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from open3d) (1.5.2)\n", "Requirement already satisfied: tqdm in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from open3d) (4.66.1)\n", "Collecting pyquaternion (from open3d)\n", "  Downloading pyquaternion-0.9.9-py3-none-any.whl.metadata (1.4 kB)\n", "Requirement already satisfied: contourpy>=1.0.1 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from matplotlib) (1.3.1)\n", "Requirement already satisfied: cycler>=0.10 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from matplotlib) (0.11.0)\n", "Requirement already satisfied: fonttools>=4.22.0 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from matplotlib) (4.51.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from matplotlib) (1.4.4)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from matplotlib) (3.2.0)\n", "Requirement already satisfied: python-dateutil>=2.7 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from matplotlib) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from pandas) (2024.1)\n", "Requirement already satisfied: tzdata>=2022.1 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from pandas) (2023.3)\n", "Requirement already satisfied: wheel<1.0,>=0.23.0 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from astunparse>=1.6.0->tensorflow) (0.44.0)\n", "Collecting flask>=3.0.0 (from open3d)\n", "  Downloading flask-3.0.3-py3-none-any.whl.metadata (3.2 kB)\n", "Collecting werkzeug>=3.0.0 (from open3d)\n", "  Downloading werkzeug-3.0.6-py3-none-any.whl.metadata (3.7 kB)\n", "Collecting plotly>=5.0.0 (from dash>=2.6.0->open3d)\n", "  Downloading plotly-6.1.1-py3-none-any.whl.metadata (6.9 kB)\n", "Requirement already satisfied: importlib-metadata in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from dash>=2.6.0->open3d) (8.7.0)\n", "Collecting retrying (from dash>=2.6.0->open3d)\n", "  Downloading retrying-1.3.4-py3-none-any.whl.metadata (6.9 kB)\n", "Requirement already satisfied: nest-asyncio in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from dash>=2.6.0->open3d) (1.6.0)\n", "Requirement already satisfied: Jinja2>=3.1.2 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from flask>=3.0.0->open3d) (3.1.4)\n", "Collecting itsdangerous>=2.1.2 (from flask>=3.0.0->open3d)\n", "  Downloading itsdangerous-2.2.0-py3-none-any.whl.metadata (1.9 kB)\n", "Requirement already satisfied: click>=8.1.3 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from flask>=3.0.0->open3d) (8.1.7)\n", "Collecting blinker>=1.6.2 (from flask>=3.0.0->open3d)\n", "  Using cached blinker-1.9.0-py3-none-any.whl.metadata (1.6 kB)\n", "Collecting rich (from keras>=3.5.0->tensorflow)\n", "  Using cached rich-14.0.0-py3-none-any.whl.metadata (18 kB)\n", "Collecting namex (from keras>=3.5.0->tensorflow)\n", "  Downloading namex-0.0.9-py3-none-any.whl.metadata (322 bytes)\n", "Collecting optree (from keras>=3.5.0->tensorflow)\n", "  Downloading optree-0.15.0-cp310-cp310-macosx_11_0_arm64.whl.metadata (48 kB)\n", "Collecting fastjsonschema>=2.15 (from nbformat>=5.7.0->open3d)\n", "  Using cached fastjsonschema-2.21.1-py3-none-any.whl.metadata (2.2 kB)\n", "Collecting jsonschema>=2.6 (from nbformat>=5.7.0->open3d)\n", "  Using cached jsonschema-4.23.0-py3-none-any.whl.metadata (7.9 kB)\n", "Requirement already satisfied: jupyter-core!=5.0.*,>=4.12 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from nbformat>=5.7.0->open3d) (5.7.2)\n", "Requirement already satisfied: traitlets>=5.1 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from nbformat>=5.7.0->open3d) (5.14.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from requests<3,>=2.21.0->tensorflow) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from requests<3,>=2.21.0->tensorflow) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from requests<3,>=2.21.0->tensorflow) (2.2.3)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from requests<3,>=2.21.0->tensorflow) (2025.4.26)\n", "Requirement already satisfied: joblib>=1.2.0 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from scikit-learn>=0.21->open3d) (1.4.2)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from scikit-learn>=0.21->open3d) (3.5.0)\n", "Collecting markdown>=2.6.8 (from tensorboard~=2.19.0->tensorflow)\n", "  Downloading markdown-3.8-py3-none-any.whl.metadata (5.1 kB)\n", "Collecting tensorboard-data-server<0.8.0,>=0.7.0 (from tensorboard~=2.19.0->tensorflow)\n", "  Using cached tensorboard_data_server-0.7.2-py3-none-any.whl.metadata (1.1 kB)\n", "Requirement already satisfied: MarkupSafe>=2.1.1 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from werkzeug>=3.0.0->open3d) (2.1.3)\n", "Requirement already satisfied: attrs>=22.2.0 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (24.3.0)\n", "Collecting jsonschema-specifications>=2023.03.6 (from jsonschema>=2.6->nbformat>=5.7.0->open3d)\n", "  Using cached jsonschema_specifications-2025.4.1-py3-none-any.whl.metadata (2.9 kB)\n", "Collecting referencing>=0.28.4 (from jsonschema>=2.6->nbformat>=5.7.0->open3d)\n", "  Using cached referencing-0.36.2-py3-none-any.whl.metadata (2.8 kB)\n", "Collecting rpds-py>=0.7.1 (from jsonschema>=2.6->nbformat>=5.7.0->open3d)\n", "  Downloading rpds_py-0.25.1-cp310-cp310-macosx_11_0_arm64.whl.metadata (4.1 kB)\n", "Requirement already satisfied: platformdirs>=2.5 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from jupyter-core!=5.0.*,>=4.12->nbformat>=5.7.0->open3d) (4.3.8)\n", "Collecting narwhals>=1.15.1 (from plotly>=5.0.0->dash>=2.6.0->open3d)\n", "  Downloading narwhals-1.40.0-py3-none-any.whl.metadata (11 kB)\n", "Requirement already satisfied: zipp>=3.20 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from importlib-metadata->dash>=2.6.0->open3d) (3.21.0)\n", "Collecting markdown-it-py>=2.2.0 (from rich->keras>=3.5.0->tensorflow)\n", "  Using cached markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /Users/<USER>/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages (from rich->keras>=3.5.0->tensorflow) (2.19.1)\n", "Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich->keras>=3.5.0->tensorflow)\n", "  Using cached mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)\n", "Downloading tensorflow-2.19.0-cp310-cp310-macosx_12_0_arm64.whl (252.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m252.5/252.5 MB\u001b[0m \u001b[31m22.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n", "\u001b[?25hDownloading open3d-0.19.0-cp310-cp310-macosx_10_15_universal2.whl (103.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m103.1/103.1 MB\u001b[0m \u001b[31m9.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m0:01\u001b[0mm\n", "\u001b[?25hDownloading laspy-2.5.4-py3-none-any.whl (84 kB)\n", "Downloading transforms3d-0.4.2-py3-none-any.whl (1.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.4/1.4 MB\u001b[0m \u001b[31m22.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading absl_py-2.2.2-py3-none-any.whl (135 kB)\n", "Using cached astunparse-1.6.3-py2.py3-none-any.whl (12 kB)\n", "Downloading dash-3.0.4-py3-none-any.whl (7.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7.9/7.9 MB\u001b[0m \u001b[31m21.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading flask-3.0.3-py3-none-any.whl (101 kB)\n", "Using cached flatbuffers-25.2.10-py2.py3-none-any.whl (30 kB)\n", "Using cached gast-0.6.0-py3-none-any.whl (21 kB)\n", "Using cached google_pasta-0.2.0-py3-none-any.whl (57 kB)\n", "Downloading grpcio-1.71.0-cp310-cp310-macosx_12_0_universal2.whl (11.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m11.3/11.3 MB\u001b[0m \u001b[31m24.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading h5py-3.13.0-cp310-cp310-macosx_11_0_arm64.whl (2.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.9/2.9 MB\u001b[0m \u001b[31m22.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading keras-3.10.0-py3-none-any.whl (1.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.4/1.4 MB\u001b[0m \u001b[31m16.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hUsing cached libclang-18.1.1-1-py2.py3-none-macosx_11_0_arm64.whl (25.8 MB)\n", "Downloading ml_dtypes-0.5.1-cp310-cp310-macosx_10_9_universal2.whl (671 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m671.5/671.5 kB\u001b[0m \u001b[31m17.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hUsing cached nbformat-5.10.4-py3-none-any.whl (78 kB)\n", "Using cached opt_einsum-3.4.0-py3-none-any.whl (71 kB)\n", "Using cached protobuf-5.29.4-cp38-abi3-macosx_10_9_universal2.whl (417 kB)\n", "Downloading tensorboard-2.19.0-py3-none-any.whl (5.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.5/5.5 MB\u001b[0m \u001b[31m23.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading tensorflow_io_gcs_filesystem-0.37.1-cp310-cp310-macosx_12_0_arm64.whl (3.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.5/3.5 MB\u001b[0m \u001b[31m22.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading termcolor-3.1.0-py3-none-any.whl (7.7 kB)\n", "Downloading werkzeug-3.0.6-py3-none-any.whl (227 kB)\n", "Downloading wrapt-1.17.2-cp310-cp310-macosx_11_0_arm64.whl (38 kB)\n", "Using cached addict-2.4.0-py3-none-any.whl (3.8 kB)\n", "Using cached ConfigArgParse-1.7-py3-none-any.whl (25 kB)\n", "Using cached pyquaternion-0.9.9-py3-none-any.whl (14 kB)\n", "Using cached blinker-1.9.0-py3-none-any.whl (8.5 kB)\n", "Using cached fastjsonschema-2.21.1-py3-none-any.whl (23 kB)\n", "Downloading itsdangerous-2.2.0-py3-none-any.whl (16 kB)\n", "Using cached jsonschema-4.23.0-py3-none-any.whl (88 kB)\n", "Downloading markdown-3.8-py3-none-any.whl (106 kB)\n", "Downloading plotly-6.1.1-py3-none-any.whl (16.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m16.1/16.1 MB\u001b[0m \u001b[31m24.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hUsing cached tensorboard_data_server-0.7.2-py3-none-any.whl (2.4 kB)\n", "Downloading namex-0.0.9-py3-none-any.whl (5.8 kB)\n", "Downloading optree-0.15.0-cp310-cp310-macosx_11_0_arm64.whl (329 kB)\n", "Using cached retrying-1.3.4-py3-none-any.whl (11 kB)\n", "Using cached rich-14.0.0-py3-none-any.whl (243 kB)\n", "Using cached jsonschema_specifications-2025.4.1-py3-none-any.whl (18 kB)\n", "Using cached markdown_it_py-3.0.0-py3-none-any.whl (87 kB)\n", "Downloading narwhals-1.40.0-py3-none-any.whl (357 kB)\n", "Using cached referencing-0.36.2-py3-none-any.whl (26 kB)\n", "Downloading rpds_py-0.25.1-cp310-cp310-macosx_11_0_arm64.whl (358 kB)\n", "Using cached mdurl-0.1.2-py3-none-any.whl (10.0 kB)\n", "Installing collected packages: namex, libclang, flatbuffers, fastjsonschema, addict, wrapt, werkzeug, transforms3d, termcolor, tensorflow-io-gcs-filesystem, tensorboard-data-server, rpds-py, retrying, pyquaternion, protobuf, optree, opt-einsum, narwhals, ml-dtypes, mdurl, markdown, laspy, itsdangerous, h5py, grpcio, google-pasta, gast, configargparse, blinker, astunparse, absl-py, tensorboard, referencing, plotly, markdown-it-py, flask, rich, jsonschema-specifications, dash, keras, jsonschema, tensorflow, nbformat, open3d\n", "Successfully installed absl-py-2.2.2 addict-2.4.0 astunparse-1.6.3 blinker-1.9.0 configargparse-1.7 dash-3.0.4 fastjsonschema-2.21.1 flask-3.0.3 flatbuffers-25.2.10 gast-0.6.0 google-pasta-0.2.0 grpcio-1.71.0 h5py-3.13.0 itsdangerous-2.2.0 jsonschema-4.23.0 jsonschema-specifications-2025.4.1 keras-3.10.0 laspy-2.5.4 libclang-18.1.1 markdown-3.8 markdown-it-py-3.0.0 mdurl-0.1.2 ml-dtypes-0.5.1 namex-0.0.9 narwhals-1.40.0 nbformat-5.10.4 open3d-0.19.0 opt-einsum-3.4.0 optree-0.15.0 plotly-6.1.1 protobuf-5.29.4 pyquaternion-0.9.9 referencing-0.36.2 retrying-1.3.4 rich-14.0.0 rpds-py-0.25.1 tensorboard-2.19.0 tensorboard-data-server-0.7.2 tensorflow-2.19.0 tensorflow-io-gcs-filesystem-0.37.1 termcolor-3.1.0 transforms3d-0.4.2 werkzeug-3.0.6 wrapt-1.17.2\n"]}], "source": ["!pip install tensorflow open3d matplotlib laspy transforms3d scipy pandas \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "cannot import name 'version' from 'importlib_metadata' (unknown location)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "Cell \u001b[0;32mIn[1], line 9\u001b[0m\n\u001b[1;32m      7\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01ms<PERSON>arn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m<PERSON>luster\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m DBSCAN\n\u001b[1;32m      8\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mscipy\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mspatial\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m ConvexHull, Delaunay\n\u001b[0;32m----> 9\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mopen3d\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mo3d\u001b[39;00m\n\u001b[1;32m     10\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mmatplotlib\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpatches\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Circle\n\u001b[1;32m     11\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mmatplotlib\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcolors\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mmcolors\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages/open3d/__init__.py:127\u001b[0m\n\u001b[1;32m    123\u001b[0m                 submodules[subname] \u001b[38;5;241m=\u001b[39m sys\u001b[38;5;241m.\u001b[39mmodules[modname]\n\u001b[1;32m    124\u001b[0m     sys\u001b[38;5;241m.\u001b[39mmodules\u001b[38;5;241m.\u001b[39mupdate(submodules)\n\u001b[0;32m--> 127\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mopen3d\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mvisualization\u001b[39;00m\n\u001b[1;32m    129\u001b[0m _insert_pybind_names(skip_names\u001b[38;5;241m=\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mml\u001b[39m\u001b[38;5;124m\"\u001b[39m,))\n\u001b[1;32m    131\u001b[0m __version__ \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m0.19.0\u001b[39m\u001b[38;5;124m\"\u001b[39m\n", "File \u001b[0;32m~/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages/open3d/visualization/__init__.py:19\u001b[0m\n\u001b[1;32m     16\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mopen3d\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpybind\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mvisualization\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;241m*\u001b[39m\n\u001b[1;32m     18\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_external_visualizer\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;241m*\u001b[39m\n\u001b[0;32m---> 19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdraw_plotly\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m draw_plotly\n\u001b[1;32m     20\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdraw_plotly\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m draw_plotly_server\n\u001b[1;32m     21\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mto_mitsuba\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m to_mitsuba\n", "File \u001b[0;32m~/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages/open3d/visualization/draw_plotly.py:12\u001b[0m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mnp\u001b[39;00m\n\u001b[1;32m     10\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mplotly\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mgraph_objects\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mgo\u001b[39;00m\n\u001b[0;32m---> 12\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mdash\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m html\n\u001b[1;32m     13\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mdash\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m dcc\n\u001b[1;32m     14\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mdash\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Dash\n", "File \u001b[0;32m~/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages/dash/__init__.py:36\u001b[0m\n\u001b[1;32m     29\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackground_callback\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (  \u001b[38;5;66;03m# noqa: F401,E402\u001b[39;00m\n\u001b[1;32m     30\u001b[0m     CeleryManager,\n\u001b[1;32m     31\u001b[0m     DiskcacheManager,\n\u001b[1;32m     32\u001b[0m )\n\u001b[1;32m     35\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_pages\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m register_page, PAGE_REGISTRY \u001b[38;5;28;01mas\u001b[39;00m page_registry  \u001b[38;5;66;03m# noqa: F401,E402\u001b[39;00m\n\u001b[0;32m---> 36\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdash\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (  \u001b[38;5;66;03m# noqa: F401,E402\u001b[39;00m\n\u001b[1;32m     37\u001b[0m     Dash,\n\u001b[1;32m     38\u001b[0m     no_update,\n\u001b[1;32m     39\u001b[0m     page_container,\n\u001b[1;32m     40\u001b[0m )\n\u001b[1;32m     41\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_patch\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Patch  \u001b[38;5;66;03m# noqa: F401,E402\u001b[39;00m\n\u001b[1;32m     42\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_jupyter\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m jupyter_dash  \u001b[38;5;66;03m# noqa: F401,E402\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/aerial_thromography/lib/python3.10/site-packages/dash/dash.py:25\u001b[0m\n\u001b[1;32m     21\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mtyping\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Any, Callable, Dict, Optional, Union, Sequence, Literal\n\u001b[1;32m     23\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mflask\u001b[39;00m\n\u001b[0;32m---> 25\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mimportlib_metadata\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m version \u001b[38;5;28;01mas\u001b[39;00m _get_distribution_version\n\u001b[1;32m     27\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mdash\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m dcc\n\u001b[1;32m     28\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mdash\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m html\n", "\u001b[0;31mImportError\u001b[0m: cannot import name 'version' from 'importlib_metadata' (unknown location)"]}], "source": ["# Import standard libraries\n", "import os\n", "import sys\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "\n", "from sklearn.cluster import DBSCAN\n", "from scipy.spatial import ConvexHull, Delaunay\n", "\n", "import open3d as o3d\n", "from matplotlib.patches import Circle\n", "import matplotlib.colors as mcolors\n", "from shapely.geometry import Point, Polygon\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def detect_pile_foundations(points, ground_z=None, min_height=0.1, max_height=1.0, \n", "                           dbscan_eps=0.1, min_samples=10, visualize=True):\n", "    \"\"\"\n", "    Detect pile foundations in point cloud data using height-based filtering and clustering.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud data (N x 3)\n", "    ground_z : float, optional\n", "        Ground level Z-coordinate. If None, it will be estimated.\n", "    min_height : float\n", "        Minimum height of piles above ground\n", "    max_height : float\n", "        Maximum height of piles above ground\n", "    dbscan_eps : float\n", "        DBSCAN epsilon parameter for clustering\n", "    min_samples : int\n", "        Minimum number of points in a cluster\n", "    visualize : bool\n", "        Whether to visualize the detection results\n", "        \n", "    Returns:\n", "    --------\n", "    pile_features : list of dict\n", "        Detected pile features with position, radius, and height information\n", "    \"\"\"\n", "    # Estimate ground level if not provided\n", "    if ground_z is None:\n", "        # Use the 5th percentile of z-coordinates as approximate ground level\n", "        ground_z = np.percentile(points[:, 2], 5)\n", "        print(f\"Estimated ground level: z = {ground_z:.3f}\")\n", "    \n", "    # Filter points based on height above ground\n", "    height_above_ground = points[:, 2] - ground_z\n", "    mask = (height_above_ground >= min_height) & (height_above_ground <= max_height)\n", "    filtered_points = points[mask]\n", "    \n", "    if len(filtered_points) < min_samples:\n", "        print(f\"Warning: Not enough points ({len(filtered_points)}) after height filtering\")\n", "        return []\n", "    \n", "    print(f\"Filtered {len(filtered_points)} points between {min_height}m and {max_height}m above ground\")\n", "    \n", "    # Use DBSCAN to cluster points (potential pile foundations)\n", "    # We only use X and Y coordinates for clustering\n", "    db = DBSCAN(eps=dbscan_eps, min_samples=min_samples).fit(filtered_points[:, :2])\n", "    labels = db.labels_\n", "    \n", "    # Number of clusters (excluding noise points with label -1)\n", "    n_clusters = len(set(labels)) - (1 if -1 in labels else 0)\n", "    print(f\"Found {n_clusters} potential pile clusters\")\n", "    \n", "    if n_clusters == 0:\n", "        return []\n", "    \n", "    # Extract features for each detected pile\n", "    pile_features = []\n", "    \n", "    # Create a colormap for visualization\n", "    colors = plt.cm.tab20(np.linspace(0, 1, n_clusters))\n", "    \n", "    if visualize:\n", "        plt.figure(figsize=(12, 10))\n", "        # Plot all filtered points\n", "        plt.scatter(filtered_points[:, 0], filtered_points[:, 1], c='lightgray', s=1, alpha=0.5)\n", "    \n", "    # Process each cluster\n", "    for i in range(n_clusters):\n", "        cluster_points = filtered_points[labels == i]\n", "        \n", "        # Calculate center as mean of x, y coordinates\n", "        center_xy = np.mean(cluster_points[:, :2], axis=0)\n", "        \n", "        # Calculate average height (z) of the cluster\n", "        avg_z = np.mean(cluster_points[:, 2])\n", "        \n", "        # Calculate pile radius as the average distance from points to center\n", "        distances = np.sqrt(np.sum((cluster_points[:, :2] - center_xy)**2, axis=1))\n", "        radius = np.mean(distances)\n", "        \n", "        # Store pile feature\n", "        pile_feature = {\n", "            'center': [center_xy[0], center_xy[1], avg_z],\n", "            'radius': radius,\n", "            'height': avg_z - ground_z,\n", "            'num_points': len(cluster_points)\n", "        }\n", "        pile_features.append(pile_feature)\n", "        \n", "        if visualize:\n", "            # Plot cluster points\n", "            plt.scatter(cluster_points[:, 0], cluster_points[:, 1], \n", "                       c=[colors[i]], s=10, alpha=0.7, label=f\"Pile {i+1}\")\n", "            \n", "            # Plot pile center and circle\n", "            plt.scatter(center_xy[0], center_xy[1], c='black', s=30, marker='x')\n", "            circle = Circle(center_xy, radius, fill=False, color=colors[i], linewidth=2)\n", "            plt.gca().add_patch(circle)\n", "            \n", "            # Add text annotation\n", "            plt.text(center_xy[0], center_xy[1] + radius + 0.1, \n", "                    f\"Pile {i+1}\\nH={pile_feature['height']:.2f}m\\nR={radius:.2f}m\",\n", "                    ha='center', va='bottom', bbox=dict(boxstyle=\"round,pad=0.3\", fc=\"white\", ec=\"gray\", alpha=0.7))\n", "    \n", "    if visualize and n_clusters > 0:\n", "        plt.title(f\"Detected Pile Foundations ({n_clusters} piles)\")\n", "        plt.xlabel(\"X (m)\")\n", "        plt.ylabel(\"Y (m)\")\n", "        plt.axis('equal')\n", "        plt.grid(True, alpha=0.3)\n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    return pile_features"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def verify_pile_positions(detected_piles, reference_piles, tolerance=0.5, visualize=True):\n", "    \"\"\"\n", "    Verify detected pile positions against reference positions (e.g., from CAD design).\n", "    \n", "    Parameters:\n", "    -----------\n", "    detected_piles : list of dict\n", "        Detected pile features from point cloud\n", "    reference_piles : list of dict\n", "        Reference pile features from CAD or design files\n", "    tolerance : float\n", "        Maximum allowable distance between detected and reference piles (in meters)\n", "    visualize : bool\n", "        Whether to visualize the verification results\n", "        \n", "    Returns:\n", "    --------\n", "    verification_results : dict\n", "        Verification results including matches, missing piles, and extra piles\n", "    \"\"\"\n", "    if not detected_piles or not reference_piles:\n", "        print(\"Warning: Empty pile list provided\")\n", "        return {\n", "            'matches': [],\n", "            'missing': reference_piles,\n", "            'extra': detected_piles,\n", "            'match_rate': 0.0\n", "        }\n", "    \n", "    # Extract centers of detected and reference piles\n", "    detected_centers = np.array([pile['center'][:2] for pile in detected_piles])\n", "    reference_centers = np.array([pile['center'][:2] for pile in reference_piles])\n", "    \n", "    # Initialize matching arrays\n", "    matches = []\n", "    matched_detected = [False] * len(detected_piles)\n", "    matched_reference = [False] * len(reference_piles)\n", "    \n", "    # For each reference pile, find the closest detected pile within tolerance\n", "    for i, ref_center in enumerate(reference_centers):\n", "        # Calculate distances to all detected piles\n", "        distances = np.sqrt(np.sum((detected_centers - ref_center)**2, axis=1))\n", "        \n", "        # Find the closest detected pile\n", "        if len(distances) > 0:\n", "            closest_idx = np.argmin(distances)\n", "            min_distance = distances[closest_idx]\n", "            \n", "            # If within tolerance and not already matched\n", "            if min_distance <= tolerance and not matched_detected[closest_idx]:\n", "                matches.append({\n", "                    'reference_idx': i,\n", "                    'detected_idx': closest_idx,\n", "                    'distance': min_distance,\n", "                    'reference': reference_piles[i],\n", "                    'detected': detected_piles[closest_idx]\n", "                })\n", "                matched_detected[closest_idx] = True\n", "                matched_reference[i] = True\n", "    \n", "    # Identify missing and extra piles\n", "    missing_piles = [pile for i, pile in enumerate(reference_piles) if not matched_reference[i]]\n", "    extra_piles = [pile for i, pile in enumerate(detected_piles) if not matched_detected[i]]\n", "    \n", "    # Calculate match rate\n", "    match_rate = len(matches) / len(reference_piles) if reference_piles else 0.0\n", "    \n", "    # Print summary\n", "    print(f\"Verification Results:\")\n", "    print(f\"  - Matched: {len(matches)} piles\")\n", "    print(f\"  - Missing: {len(missing_piles)} piles\")\n", "    print(f\"  - Extra: {len(extra_piles)} piles\")\n", "    print(f\"  - Match Rate: {match_rate:.1%}\")\n", "    \n", "    # Visualize results if requested\n", "    if visualize:\n", "        plt.figure(figsize=(14, 10))\n", "        \n", "        # Plot reference piles\n", "        for i, pile in enumerate(reference_piles):\n", "            center = pile['center'][:2]\n", "            radius = pile['radius']\n", "            circle = Circle(center, radius, fill=False, edgecolor='blue', linestyle='--', linewidth=2, alpha=0.7)\n", "            plt.gca().add_patch(circle)\n", "            plt.scatter(center[0], center[1], c='blue', s=50, marker='o', alpha=0.7)\n", "            plt.text(center[0], center[1] + radius + 0.2, f\"Ref {i+1}\", color='blue', ha='center')\n", "        \n", "        # Plot detected piles\n", "        for i, pile in enumerate(detected_piles):\n", "            center = pile['center'][:2]\n", "            radius = pile['radius']\n", "            \n", "            # Determine color based on matching status\n", "            if matched_detected[i]:\n", "                color = 'green'\n", "                alpha = 0.7\n", "            else:\n", "                color = 'red'\n", "                alpha = 0.5\n", "                \n", "            circle = Circle(center, radius, fill=False, edgecolor=color, linewidth=2, alpha=alpha)\n", "            plt.gca().add_patch(circle)\n", "            plt.scatter(center[0], center[1], c=color, s=30, marker='x')\n", "            plt.text(center[0], center[1] - radius - 0.2, f\"Det {i+1}\", color=color, ha='center')\n", "        \n", "        # Plot connections between matched piles\n", "        for match in matches:\n", "            ref_center = match['reference']['center'][:2]\n", "            det_center = match['detected']['center'][:2]\n", "            plt.plot([ref_center[0], det_center[0]], [ref_center[1], det_center[1]], \n", "                    'g-', alpha=0.5, linewidth=1)\n", "            \n", "            # Add distance annotation\n", "            mid_point = [(ref_center[0] + det_center[0])/2, (ref_center[1] + det_center[1])/2]\n", "            plt.text(mid_point[0], mid_point[1], f\"{match['distance']:.2f}m\", \n", "                    ha='center', va='center', fontsize=8,\n", "                    bbox=dict(boxstyle=\"round,pad=0.1\", fc=\"white\", ec=\"gray\", alpha=0.7))\n", "        \n", "        # Add legend\n", "        plt.plot([], [], 'bo', label='Reference Pile', alpha=0.7)\n", "        plt.plot([], [], 'gx', label='Matched Detected Pile', alpha=0.7)\n", "        plt.plot([], [], 'rx', label='Extra Detected Pile', alpha=0.7)\n", "        plt.plot([], [], 'b--', label='Reference Outline', alpha=0.7)\n", "        plt.plot([], [], 'g-', label='Matched Pile', alpha=0.7)\n", "        \n", "        plt.title(f\"Pile Foundation Verification (Match Rate: {match_rate:.1%})\")\n", "        plt.xlabel(\"X (m)\")\n", "        plt.ylabel(\"Y (m)\")\n", "        plt.axis('equal')\n", "        plt.grid(True, alpha=0.3)\n", "        plt.legend(loc='upper right')\n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    return {\n", "        'matches': matches,\n", "        'missing': missing_piles,\n", "        'extra': extra_piles,\n", "        'match_rate': match_rate\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example Usage\n", "\n", "Below is an example of how to use the pile detection and verification functions. You'll need to adapt this to your specific point cloud data and CAD design."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Load point cloud data (replace with your actual data loading code)\n", "def load_example_point_cloud():\n", "    # This is just a placeholder. In a real scenario, you would load data from a file.\n", "    # Example of creating synthetic data for demonstration\n", "    np.random.seed(42)  # For reproducibility\n", "    \n", "    # Create ground points\n", "    ground_points = np.random.rand(1000, 3)\n", "    ground_points[:, 2] = np.random.normal(0, 0.05, 1000)  # Ground at z=0 with some noise\n", "    \n", "    # Create pile 1\n", "    pile1_center = np.array([3, 3, 0.5])\n", "    pile1_radius = 0.3\n", "    pile1_points = create_synthetic_pile(pile1_center, pile1_radius, 100)\n", "    \n", "    # Create pile 2\n", "    pile2_center = np.array([5, 5, 0.6])\n", "    pile2_radius = 0.25\n", "    pile2_points = create_synthetic_pile(pile2_center, pile2_radius, 80)\n", "    \n", "    # Create pile 3\n", "    pile3_center = np.array([7, 3, 0.4])\n", "    pile3_radius = 0.35\n", "    pile3_points = create_synthetic_pile(pile3_center, pile3_radius, 120)\n", "    \n", "    # <PERSON><PERSON><PERSON> all points\n", "    all_points = np.vstack([ground_points, pile1_points, pile2_points, pile3_points])\n", "    \n", "    return all_points\n", "\n", "def create_synthetic_pile(center, radius, num_points):\n", "    # Create points around a cylinder to simulate a pile\n", "    angles = np.random.uniform(0, 2*np.pi, num_points)\n", "    radii = np.random.normal(radius, 0.05, num_points)  # Add some noise to radius\n", "    heights = np.random.uniform(0, 0.7, num_points)  # Vary the height\n", "    \n", "    # Calculate points on the cylinder\n", "    x = center[0] + radii * np.cos(angles)\n", "    y = center[1] + radii * np.sin(angles)\n", "    z = center[2] + heights\n", "    \n", "    return np.column_stack([x, y, z])\n", "\n", "# Example reference piles from CAD design\n", "reference_piles = [\n", "    {'center': [3, 3, 0.5], 'radius': 0.3, 'height': 0.5},\n", "    {'center': [5, 5, 0.5], 'radius': 0.3, 'height': 0.5},\n", "    {'center': [7, 3, 0.5], 'radius': 0.3, 'height': 0.5},\n", "    {'center': [9, 9, 0.5], 'radius': 0.3, 'height': 0.5}  # This one doesn't exist in the point cloud\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load example point cloud\n", "point_cloud = load_example_point_cloud()\n", "\n", "# Detect pile foundations\n", "detected_piles = detect_pile_foundations(\n", "    points=point_cloud,\n", "    ground_z=0.0,  # We know ground is at z=0 in our synthetic data\n", "    min_height=0.2,\n", "    max_height=1.0,\n", "    dbscan_eps=0.4,  # Larger epsilon for our synthetic data\n", "    min_samples=5,   # Fewer samples required for our synthetic data\n", "    visualize=True\n", ")\n", "\n", "# Verify pile positions against reference\n", "verification_results = verify_pile_positions(\n", "    detected_piles=detected_piles,\n", "    reference_piles=reference_piles,\n", "    tolerance=0.5,  # 50cm tolerance\n", "    visualize=True\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Reporting and Analysis\n", "\n", "Based on the verification results, we can generate reports and analysis to help with quality assessment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_pile_verification_report(verification_results, output_file=None):\n", "    \"\"\"\n", "    Generate a detailed report of pile verification results.\n", "    \n", "    Parameters:\n", "    -----------\n", "    verification_results : dict\n", "        Results from the verify_pile_positions function\n", "    output_file : str, optional\n", "        Path to save the report (if None, just prints to console)\n", "    \"\"\"\n", "    matches = verification_results['matches']\n", "    missing = verification_results['missing']\n", "    extra = verification_results['extra']\n", "    match_rate = verification_results['match_rate']\n", "    \n", "    # Prepare report text\n", "    report = [\n", "        \"# Pile Foundation Verification Report\",\n", "        f\"Generated on: {import_datetime().now().strftime('%Y-%m-%d %H:%M:%S')}\",\n", "        \"\",\n", "        \"## Summary\",\n", "        f\"- Total reference piles: {len(matches) + len(missing)}\",\n", "        f\"- Total detected piles: {len(matches) + len(extra)}\",\n", "        f\"- Matched piles: {len(matches)}\",\n", "        f\"- Missing piles: {len(missing)}\",\n", "        f\"- Extra piles: {len(extra)}\",\n", "        f\"- Match rate: {match_rate:.1%}\",\n", "        \"\",\n", "        \"## Matched Piles\"\n", "    ]\n", "    \n", "    # Add matched pile details\n", "    if matches:\n", "        report.append(\"| Ref # | Det # | Distance (m) | Ref <PERSON>tion | Detected Position | Ref <PERSON>dius | Det Radius |\")\n", "        report.append(\"|-------|-------|-------------|--------------|-------------------|------------|------------|\")\n", "        \n", "        for i, match in enumerate(matches):\n", "            ref_pos = match['reference']['center']\n", "            det_pos = match['detected']['center']\n", "            ref_radius = match['reference']['radius']\n", "            det_radius = match['detected']['radius']\n", "            \n", "            report.append(f\"| {match['reference_idx']+1} | {match['detected_idx']+1} | {match['distance']:.3f} | \"\n", "                         f\"({ref_pos[0]:.2f}, {ref_pos[1]:.2f}, {ref_pos[2]:.2f}) | \"\n", "                         f\"({det_pos[0]:.2f}, {det_pos[1]:.2f}, {det_pos[2]:.2f}) | \"\n", "                         f\"{ref_radius:.3f} | {det_radius:.3f} |\")\n", "    else:\n", "        report.append(\"No matched piles found.\")\n", "    \n", "    # Add missing pile details\n", "    report.extend([\"\", \"## <PERSON> Piles\"])\n", "    \n", "    if missing:\n", "        report.append(\"| Ref # | Position | Radius |\")\n", "        report.append(\"|-------|----------|--------|\")\n", "        \n", "        for i, pile in enumerate(missing):\n", "            pos = pile['center']\n", "            report.append(f\"| {i+1} | ({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f}) | {pile['radius']:.3f} |\")\n", "    else:\n", "        report.append(\"No missing piles.\")\n", "    \n", "    # Add extra pile details\n", "    report.extend([\"\", \"## <PERSON> Piles\"])\n", "    \n", "    if extra:\n", "        report.append(\"| Det # | Position | Radius | Height |\")\n", "        report.append(\"|-------|----------|--------|--------|\")\n", "        \n", "        for i, pile in enumerate(extra):\n", "            pos = pile['center']\n", "            report.append(f\"| {i+1} | ({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f}) | \"\n", "                         f\"{pile['radius']:.3f} | {pile['height']:.3f} |\")\n", "    else:\n", "        report.append(\"No extra piles.\")\n", "    \n", "    # Add conclusion\n", "    report.extend([\"\", \"## Conclusion\"])\n", "    \n", "    if match_rate >= 0.95:\n", "        report.append(\"✅ PASS: Excellent match between reference and detected piles.\")\n", "    elif match_rate >= 0.8:\n", "        report.append(\"⚠️ ATTENTION: Good match, but some piles are missing or have positioning issues.\")\n", "    else:\n", "        report.append(\"❌ FAIL: Significant discrepancies between reference and detected piles.\")\n", "    \n", "    # Print report\n", "    print(\"\\n\".join(report))\n", "    \n", "    # Save to file if requested\n", "    if output_file:\n", "        with open(output_file, 'w') as f:\n", "            f.write(\"\\n\".join(report))\n", "    \n", "    return report"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate a verification report\n", "report = generate_pile_verification_report(verification_results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this notebook, we've implemented functions for detecting pile foundations in point cloud data and verifying their positions against reference CAD designs. This quality assessment is crucial for ensuring that construction is proceeding according to plan, helping to identify any discrepancies early in the construction process.\n", "\n", "The key components of our implementation include:\n", "\n", "1. **Height-based filtering** to isolate potential pile foundations\n", "2. **DBSCAN clustering** to group points belonging to the same pile\n", "3. **Feature extraction** to determine pile positions, radii, and heights\n", "4. **Position verification** to compare detected piles with reference designs\n", "5. **Reporting and visualization** to communicate results clearly"]}], "metadata": {"kernelspec": {"display_name": "aerial_thromography", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 4}