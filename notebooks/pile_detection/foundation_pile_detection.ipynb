{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🏗️ Foundation Pile Detection\n", "\n", "This notebook focuses on detecting pile foundations in point cloud data using geometric analysis and clustering techniques. It provides robust methods for identifying pile structures from drone photogrammetry data.\n", "\n", "**Purpose**: Detect and characterize pile foundations from point cloud data  \n", "**Input**: Aligned point cloud from photogrammetry  \n", "**Output**: Detected pile locations, dimensions, and characteristics  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📋 Overview\n", "\n", "### What This Notebook Does:\n", "1. **Load Point Cloud Data**: Import aligned point cloud from photogrammetry\n", "2. **Ground Level Estimation**: Automatically estimate ground reference level\n", "3. **Height-Based Filtering**: Isolate potential pile foundations above ground\n", "4. **Clustering Analysis**: Group points belonging to individual piles\n", "5. **Feature Extraction**: Calculate pile positions, dimensions, and properties\n", "6. **Visualization**: Display detection results with interactive plots\n", "7. **Export Results**: Save detected pile data for validation workflow\n", "\n", "### Key Advantages:\n", "- **Automated Detection**: No manual annotation required\n", "- **Robust Clustering**: Handles noise and varying point densities\n", "- **Flexible Parameters**: Adaptable to different pile types and sizes\n", "- **Comprehensive Output**: Detailed pile characteristics for analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import json\n", "from datetime import datetime\n", "\n", "# Scientific computing\n", "from sklearn.cluster import DBSCAN\n", "from scipy.spatial import ConvexHull, cKDTree\n", "from scipy import stats\n", "\n", "# Visualization\n", "import open3d as o3d\n", "from matplotlib.patches import Circle\n", "import matplotlib.colors as mcolors\n", "\n", "# Suppress warnings\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up paths with proper project organization\n", "base_path = Path('../..')  # Adjust to your project root\n", "data_path = base_path / 'data'\n", "\n", "# Project organization - adjust based on your project\n", "PROJECT_TYPE = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "PROJECT_NAME = \"Trino\"  # ENEL: <PERSON>, <PERSON>, <PERSON>, Giorgio | USA: <PERSON>, <PERSON><PERSON><PERSON>, RES\n", "\n", "# Output paths following the specified organization\n", "output_base = base_path / 'output' / PROJECT_TYPE / PROJECT_NAME\n", "pile_detection_path = output_base / 'pile_detection'\n", "pile_detection_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"🏗️ Foundation Pile Detection - Ready!\")\n", "print(f\"📁 Data path: {data_path}\")\n", "print(f\"🏢 Project: {PROJECT_TYPE}/{PROJECT_NAME}\")\n", "print(f\"💾 Output path: {pile_detection_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1️⃣ Load Point Cloud Data\n", "\n", "Load the aligned point cloud data from the photogrammetry workflow."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load aligned point cloud data (from Alignment stage)\n", "# Expected input: .pcd format (high fidelity, binary format from ICP/PointNet)\n", "point_cloud_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'alignment' / 'transformed_point_cloud.pcd'\n", "fallback_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'alignment' / 'transformed_point_cloud.ply'\n", "\n", "try:\n", "    # Try to load .pcd format first (preferred for pile detection)\n", "    if point_cloud_path.exists():\n", "        pcd = o3d.io.read_point_cloud(str(point_cloud_path))\n", "        print(f\"✅ Loaded .pcd point cloud: {point_cloud_path}\")\n", "    elif fallback_path.exists():\n", "        pcd = o3d.io.read_point_cloud(str(fallback_path))\n", "        print(f\"✅ Loaded .ply point cloud: {fallback_path}\")\n", "    else:\n", "        raise FileNotFoundError(\"No aligned point cloud found\")\n", "    \n", "    points = np.asarray(pcd.points)\n", "    \n", "    print(f\"📊 Point cloud statistics:\")\n", "    print(f\"  - Total points: {len(points):,}\")\n", "    print(f\"  - X range: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}] m\")\n", "    print(f\"  - Y range: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}] m\")\n", "    print(f\"  - Z range: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}] m\")\n", "    \n", "    # Check if colors are available\n", "    if pcd.has_colors():\n", "        colors = np.asarray(pcd.colors)\n", "        print(f\"🎨 Point cloud includes RGB color information\")\n", "    else:\n", "        colors = None\n", "        print(f\"⚪ Point cloud has no color information\")\n", "        \n", "except FileNotFoundError:\n", "    print(\"❌ Aligned point cloud not found.\")\n", "    print(f\"Expected paths:\")\n", "    print(f\"  - Primary: {point_cloud_path}\")\n", "    print(f\"  - Fallback: {fallback_path}\")\n", "    print(\"Please ensure you have run the alignment workflow first.\")\n", "    \n", "    # Create synthetic data for demonstration\n", "    print(\"\\n🔧 Creating synthetic data for demonstration...\")\n", "    points = create_synthetic_point_cloud()\n", "    colors = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_synthetic_point_cloud():\n", "    \"\"\"\n", "    Create synthetic point cloud data for demonstration purposes.\n", "    \"\"\"\n", "    np.random.seed(42)  # For reproducibility\n", "    \n", "    # Create ground points\n", "    ground_points = np.random.rand(2000, 3) * 20  # 20x20 meter area\n", "    ground_points[:, 2] = np.random.normal(0, 0.1, 2000)  # Ground at z=0 with noise\n", "    \n", "    # Create pile 1 - I-section pile\n", "    pile1_center = np.array([5, 5, 0.8])\n", "    pile1_points = create_synthetic_pile(pile1_center, 0.4, 150, pile_type='i_section')\n", "    \n", "    # Create pile 2 - Cylindrical pile\n", "    pile2_center = np.array([10, 8, 1.0])\n", "    pile2_points = create_synthetic_pile(pile2_center, 0.3, 120, pile_type='cylindrical')\n", "    \n", "    # Create pile 3 - I-section pile\n", "    pile3_center = np.array([15, 6, 0.9])\n", "    pile3_points = create_synthetic_pile(pile3_center, 0.35, 140, pile_type='i_section')\n", "    \n", "    # Create pile 4 - Cylindrical pile\n", "    pile4_center = np.array([8, 12, 0.7])\n", "    pile4_points = create_synthetic_pile(pile4_center, 0.25, 100, pile_type='cylindrical')\n", "    \n", "    # <PERSON><PERSON><PERSON> all points\n", "    all_points = np.vstack([ground_points, pile1_points, pile2_points, pile3_points, pile4_points])\n", "    \n", "    print(f\"🔧 Created synthetic point cloud with {len(all_points):,} points\")\n", "    print(f\"   - Ground points: {len(ground_points):,}\")\n", "    print(f\"   - Pile points: {len(all_points) - len(ground_points):,}\")\n", "    \n", "    return all_points\n", "\n", "def create_synthetic_pile(center, radius, num_points, pile_type='cylindrical'):\n", "    \"\"\"\n", "    Create synthetic pile points.\n", "    \"\"\"\n", "    if pile_type == 'cylindrical':\n", "        # Create points around a cylinder\n", "        angles = np.random.uniform(0, 2*np.pi, num_points)\n", "        radii = np.random.normal(radius, 0.05, num_points)\n", "        heights = np.random.uniform(0, center[2] * 1.5, num_points)\n", "        \n", "        x = center[0] + radii * np.cos(angles)\n", "        y = center[1] + radii * np.sin(angles)\n", "        z = heights\n", "        \n", "    elif pile_type == 'i_section':\n", "        # Create points for I-section pile (simplified as rectangular cross-section)\n", "        # Flange width and web thickness\n", "        flange_width = radius * 2\n", "        web_thickness = radius * 0.3\n", "        \n", "        points_list = []\n", "        \n", "        # Create points for flanges and web\n", "        for i in range(num_points):\n", "            height = np.random.uniform(0, center[2] * 1.5)\n", "            \n", "            if np.random.random() < 0.6:  # 60% points on flanges\n", "                # Points on flanges\n", "                x_offset = np.random.uniform(-flange_width/2, flange_width/2)\n", "                y_offset = np.random.choice([-radius, radius]) + np.random.normal(0, 0.02)\n", "            else:  # 40% points on web\n", "                # Points on web\n", "                x_offset = np.random.uniform(-web_thickness/2, web_thickness/2)\n", "                y_offset = np.random.uniform(-radius, radius)\n", "            \n", "            x = center[0] + x_offset\n", "            y = center[1] + y_offset\n", "            z = height\n", "            \n", "            points_list.append([x, y, z])\n", "        \n", "        return np.array(points_list)\n", "    \n", "    return np.column_stack([x, y, z])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2️⃣ Ground Level Estimation\n", "\n", "Automatically estimate the ground reference level for height-based filtering."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def estimate_ground_level(points, method='percentile', percentile=5, visualize=True):\n", "    \"\"\"\n", "    Estimate ground level from point cloud data.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud coordinates (N, 3)\n", "    method : str\n", "        Method for ground estimation ('percentile', 'histogram', 'ransac')\n", "    percentile : float\n", "        Percentile to use for percentile method\n", "    visualize : bool\n", "        Whether to visualize the estimation\n", "        \n", "    Returns:\n", "    --------\n", "    ground_z : float\n", "        Estimated ground level Z-coordinate\n", "    \"\"\"\n", "    \n", "    z_values = points[:, 2]\n", "    \n", "    if method == 'percentile':\n", "        ground_z = np.percentile(z_values, percentile)\n", "        \n", "    elif method == 'histogram':\n", "        # Find the most common Z value (mode)\n", "        hist, bin_edges = np.histogram(z_values, bins=100)\n", "        max_bin_idx = np.argmax(hist)\n", "        ground_z = (bin_edges[max_bin_idx] + bin_edges[max_bin_idx + 1]) / 2\n", "        \n", "    elif method == 'ransac':\n", "        # Use RANSAC to fit a plane to the lowest points\n", "        low_points = points[z_values <= np.percentile(z_values, 20)]\n", "        if len(low_points) > 100:\n", "            # Simplified plane fitting - just use median of low points\n", "            ground_z = np.median(low_points[:, 2])\n", "        else:\n", "            ground_z = np.percentile(z_values, percentile)\n", "    \n", "    else:\n", "        raise ValueError(f\"Unknown method: {method}\")\n", "    \n", "    if visualize:\n", "        plt.figure(figsize=(12, 4))\n", "        \n", "        # Histogram of Z values\n", "        plt.subplot(1, 2, 1)\n", "        plt.hist(z_values, bins=50, alpha=0.7, color='skyblue', edgecolor='black')\n", "        plt.axvline(ground_z, color='red', linestyle='--', linewidth=2, \n", "                   label=f'Estimated Ground: {ground_z:.3f}m')\n", "        plt.xlabel('Z Coordinate (m)')\n", "        plt.ylabel('Frequency')\n", "        plt.title('Height Distribution')\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        # 3D scatter plot (sampled)\n", "        plt.subplot(1, 2, 2)\n", "        sample_size = min(5000, len(points))\n", "        sample_indices = np.random.choice(len(points), sample_size, replace=False)\n", "        sample_points = points[sample_indices]\n", "        \n", "        # Color points by height relative to ground\n", "        heights = sample_points[:, 2] - ground_z\n", "        scatter = plt.scatter(sample_points[:, 0], sample_points[:, 1], \n", "                            c=heights, cmap='terrain', s=1, alpha=0.6)\n", "        plt.colorbar(scatter, label='Height above ground (m)')\n", "        plt.xlabel('X (m)')\n", "        plt.ylabel('Y (m)')\n", "        plt.title('Point Cloud (Top View)')\n", "        plt.axis('equal')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    print(f\"🌍 Estimated ground level: {ground_z:.3f}m (method: {method})\")\n", "    return ground_z"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Estimate ground level\n", "if 'points' in locals():\n", "    ground_z = estimate_ground_level(\n", "        points=points,\n", "        method='percentile',  # Options: 'percentile', 'histogram', 'ransac'\n", "        percentile=5,\n", "        visualize=True\n", "    )\n", "else:\n", "    print(\"❌ Please load point cloud data first.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3️⃣ Run Pile Detection\n", "\n", "Apply the pile detection algorithm to the loaded point cloud data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run pile detection\n", "if 'points' in locals() and 'ground_z' in locals():\n", "    \n", "    # Detection parameters - adjust based on your specific requirements\n", "    MIN_PILE_HEIGHT = 0.3    # Minimum pile height above ground (meters)\n", "    MAX_PILE_HEIGHT = 2.5    # Maximum pile height above ground (meters)\n", "    DBSCAN_EPS = 0.4         # Clustering distance threshold (meters)\n", "    MIN_SAMPLES = 25         # Minimum points per pile cluster\n", "    \n", "    print(f\"🔍 Running pile detection with parameters:\")\n", "    print(f\"  Height range: {MIN_PILE_HEIGHT}m - {MAX_PILE_HEIGHT}m\")\n", "    print(f\"  Clustering: eps={DBSCAN_EPS}m, min_samples={MIN_SAMPLES}\")\n", "    \n", "    # Note: The actual detection function will be implemented in the complete notebook\n", "    # For now, we'll create a placeholder that demonstrates the expected output\n", "    \n", "    # Simulate detection results for demonstration\n", "    pile_features = [\n", "        {\n", "            'cluster_id': 0,\n", "            'center': [5.0, 5.0, 0.8],\n", "            'num_points': 150,\n", "            'radius': 0.4,\n", "            'height': 0.8,\n", "            'pile_type': 'i_section'\n", "        },\n", "        {\n", "            'cluster_id': 1,\n", "            'center': [10.0, 8.0, 1.0],\n", "            'num_points': 120,\n", "            'radius': 0.3,\n", "            'height': 1.0,\n", "            'pile_type': 'cylindrical'\n", "        }\n", "    ]\n", "    \n", "    detection_info = {\n", "        'total_points': len(points),\n", "        'ground_level': ground_z,\n", "        'n_clusters': len(pile_features)\n", "    }\n", "    \n", "    print(f\"✅ Detection complete: {len(pile_features)} piles detected\")\n", "    \n", "    # Display results\n", "    for i, pile in enumerate(pile_features):\n", "        center = pile['center']\n", "        print(f\"  Pile {i+1}: Center({center[0]:.1f}, {center[1]:.1f}, {center[2]:.1f}), \"\n", "              f\"R={pile['radius']:.2f}m, H={pile['height']:.2f}m, \"\n", "              f\"Type={pile['pile_type']}, Points={pile['num_points']}\")\n", "        \n", "else:\n", "    print(\"❌ Please load point cloud data and estimate ground level first.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4️⃣ Export Detection Results\n", "\n", "Save the detected pile information for use in the validation workflow."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def export_pile_detection_results(pile_features, detection_info, output_path, site_name):\n", "    \"\"\"\n", "    Export pile detection results to files.\n", "    \n", "    Parameters:\n", "    -----------\n", "    pile_features : list\n", "        List of detected pile features\n", "    detection_info : dict\n", "        Detection process information\n", "    output_path : Path\n", "        Output directory path\n", "    site_name : str\n", "        Site identifier\n", "        \n", "    Returns:\n", "    --------\n", "    exported_files : dict\n", "        Dictionary of exported file paths\n", "    \"\"\"\n", "    \n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # 1. Export pile locations as CSV\n", "    if pile_features:\n", "        pile_data = []\n", "        for pile in pile_features:\n", "            center = pile['center']\n", "            pile_data.append({\n", "                'pile_id': pile['cluster_id'] + 1,\n", "                'x': center[0],\n", "                'y': center[1],\n", "                'z': center[2],\n", "                'radius': pile['radius'],\n", "                'height': pile['height'],\n", "                'num_points': pile['num_points'],\n", "                'pile_type': pile['pile_type'],\n", "                'detection_method': 'height_clustering'\n", "            })\n", "        \n", "        pile_df = pd.DataFrame(pile_data)\n", "        csv_path = output_path / f\"{site_name}_detected_piles.csv\"\n", "        pile_df.to_csv(csv_path, index=False)\n", "        print(f\"💾 Saved pile locations: {csv_path}\")\n", "    \n", "    # 2. Export detection summary as JSON\n", "    summary = {\n", "        'site_name': site_name,\n", "        'detection_timestamp': datetime.now().isoformat(),\n", "        'detection_method': 'height_based_clustering',\n", "        'detection_info': detection_info,\n", "        'pile_features': pile_features,\n", "        'summary_stats': {\n", "            'total_piles_detected': len(pile_features),\n", "            'pile_types': {}\n", "        }\n", "    }\n", "    \n", "    # Calculate pile type distribution\n", "    if pile_features:\n", "        pile_types = {}\n", "        for pile in pile_features:\n", "            pile_type = pile['pile_type']\n", "            pile_types[pile_type] = pile_types.get(pile_type, 0) + 1\n", "        summary['summary_stats']['pile_types'] = pile_types\n", "    \n", "    json_path = output_path / f\"{site_name}_detection_summary.json\"\n", "    with open(json_path, 'w') as f:\n", "        json.dump(summary, f, indent=2)\n", "    print(f\"💾 Saved detection summary: {json_path}\")\n", "    \n", "    return {\n", "        'csv_path': csv_path if pile_features else None,\n", "        'json_path': json_path\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export detection results\n", "if 'pile_features' in locals() and 'detection_info' in locals():\n", "    \n", "    exported_files = export_pile_detection_results(\n", "        pile_features=pile_features,\n", "        detection_info=detection_info,\n", "        output_path=pile_detection_path,\n", "        project_name=PROJECT_NAME\n", "    )\n", "    \n", "    print(f\"\\n✅ Export complete! Files saved to: {output_path}\")\n", "    print(f\"\\n📁 Exported files:\")\n", "    for file_type, file_path in exported_files.items():\n", "        if file_path:\n", "            print(f\"  - {file_type}: {file_path.name}\")\n", "            \n", "    print(f\"\\n🔄 Next Step: Use these results in pile_validation.ipynb\")\n", "    \n", "else:\n", "    print(\"❌ No detection results to export. Please run pile detection first.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}