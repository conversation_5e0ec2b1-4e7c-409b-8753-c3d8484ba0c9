{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# ✅ Pile Detection Validation\n",
    "\n",
    "This notebook focuses on validating detected pile foundations against reference data (IFC metadata, CAD drawings, or manual annotations). It provides comprehensive validation metrics and analysis tools.\n",
    "\n",
    "**Purpose**: Validate and assess the accuracy of pile detection results  \n",
    "**Input**: Detected pile locations + Reference data (IFC/CAD metadata)  \n",
    "**Output**: Validation metrics, accuracy assessment, and quality reports  \n",
    "\n",
    "**Author**: Preetam Balijepalli  \n",
    "**Date**: December 2024  \n",
    "**Project**: Energy Inspection 3D"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📋 Overview\n",
    "\n",
    "### What This Notebook Does:\n",
    "1. **Load Detection Results**: Import pile detection results from foundation_pile_detection.ipynb\n",
    "2. **Load Reference Data**: Import ground truth data (IFC metadata, CAD data, or manual annotations)\n",
    "3. **Spatial Matching**: Match detected piles with reference pile locations\n",
    "4. **Calculate Metrics**: Compute precision, recall, F1-score, and spatial accuracy\n",
    "5. **Error Analysis**: Identify false positives, false negatives, and spatial errors\n",
    "6. **Visualization**: Create comprehensive validation visualizations\n",
    "7. **Generate Reports**: Export validation reports and recommendations\n",
    "\n",
    "### Key Validation Metrics:\n",
    "- **Detection Accuracy**: Precision, Recall, F1-score\n",
    "- **Spatial Accuracy**: Mean distance error, RMSE\n",
    "- **Completeness**: Percentage of reference piles detected\n",
    "- **Quality Assessment**: False positive/negative analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import required libraries\n",
    "import numpy as np\n",
    "import pandas as pd\n",
    "import matplotlib.pyplot as plt\n",
    "from pathlib import Path\n",
    "import json\n",
    "from datetime import datetime\n",
    "\n",
    "# Scientific computing\n",
    "from scipy.spatial.distance import cdist\n",
    "from scipy.spatial import cKDTree\n",
    "from sklearn.metrics import precision_recall_fscore_support, confusion_matrix\n",
    "\n",
    "# Visualization\n",
    "import seaborn as sns\n",
    "from matplotlib.patches import Circle\n",
    "import matplotlib.patches as mpatches\n",
    "\n",
    "# Suppress warnings\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Set up paths with proper project organization\n",
    "base_path = Path('../..')  # Adjust to your project root\n",
    "data_path = base_path / 'data'\n",
    "\n",
    "# Project organization - adjust based on your project\n",
    "PROJECT_TYPE = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n",
    "PROJECT_NAME = \"Trino\"  # ENEL: Trino, Castro, Mudjar, Giorgio | USA: McCarthy, RPCS, RES\n",
    "\n",
    "# Input and output paths following the specified organization\n",
    "project_base = base_path / 'output' / PROJECT_TYPE / PROJECT_NAME\n",
    "detection_path = project_base / 'pile_detection'\n",
    "validation_path = project_base / 'pile_validation'\n",
    "validation_path.mkdir(parents=True, exist_ok=True)\n",
    "\n",
    "print(\"✅ Pile Detection Validation - Ready!\")\n",
    "print(f\"📁 Data path: {data_path}\")\n",
    "print(f\"🏢 Project: {PROJECT_TYPE}/{PROJECT_NAME}\")\n",
    "print(f\"🔍 Detection results path: {detection_path}\")\n",
    "print(f\"💾 Validation output path: {validation_path}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1️⃣ Load Detection Results\n",
    "\n",
    "Load the pile detection results from the foundation_pile_detection.ipynb workflow."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load detection results\n",
    "# Expected input: CSV with detected pile centers + type (from Pile Detection stage)\n",
    "detection_csv_path = detection_path / f\"{PROJECT_NAME}_detected_pile_centers.csv\"\n",
    "detection_json_path = detection_path / f\"{PROJECT_NAME}_pile_detection_metadata.json\"\n",
    "\n",
    "detected_piles = None\n",
    "detection_summary = None\n",
    "\n",
    "try:\n",
    "    # Load detected pile locations\n",
    "    detected_piles = pd.read_csv(detection_csv_path)\n",
    "    print(f\"✅ Loaded detected piles: {len(detected_piles)} piles\")\n",
    "    print(f\"📊 Columns: {list(detected_piles.columns)}\")\n",
    "    \n",
    "    # Load detection summary\n",
    "    with open(detection_json_path, 'r') as f:\n",
    "        detection_summary = json.load(f)\n",
    "    print(f\"✅ Loaded detection summary\")\n",
    "    \n",
    "    # Display sample detected piles\n",
    "    print(\"\\n📋 Sample detected piles:\")\n",
    "    display(detected_piles.head())\n",
    "    \n",
    "except FileNotFoundError as e:\n",
    "    print(f\"❌ Detection results not found: {e}\")\n",
    "    print(f\"Expected paths:\")\n",
    "    print(f\"  - {detection_csv_path}\")\n",
    "    print(f\"  - {detection_json_path}\")\n",
    "    print(\"Please run foundation_pile_detection.ipynb first.\")\n",
    "    \n",
    "    # Create synthetic detection data for demonstration\n",
    "    print(\"\\n🔧 Creating synthetic detection data for demonstration...\")\n",
    "    detected_piles = pd.DataFrame({\n",
    "        'pile_id': [1, 2, 3, 4],\n",
    "        'x': [5.0, 10.0, 15.0, 8.0],\n",
    "        'y': [5.0, 8.0, 6.0, 12.0],\n",
    "        'z': [0.8, 1.0, 0.9, 0.7],\n",
    "        'radius': [0.4, 0.3, 0.35, 0.25],\n",
    "        'height': [0.8, 1.0, 0.9, 0.7],\n",
    "        'num_points': [150, 120, 140, 100],\n",
    "        'pile_type': ['i_section', 'cylindrical', 'i_section', 'cylindrical'],\n",
    "        'detection_method': ['height_clustering'] * 4\n",
    "    })\n",
    "    \n",
    "    detection_summary = {\n",
    "        'site_name': site_name,\n",
    "        'detection_method': 'height_based_clustering',\n",
    "        'summary_stats': {\n",
    "            'total_piles_detected': len(detected_piles)\n",
    "        }\n",
    "    }\n",
    "    \n",
    "    print(f\"🔧 Created {len(detected_piles)} synthetic detected piles\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2️⃣ Load Reference Data\n",
    "\n",
    "Load ground truth reference data for validation."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load reference data (from Preprocessing stage)\n",
    "# Expected input: IFC/CAD metadata in .json or .csv format\n",
    "reference_json_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'preprocessing' / f'{PROJECT_NAME}_ifc_metadata.json'\n",
    "reference_csv_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'preprocessing' / f'{PROJECT_NAME}_pile_positions.csv'\n",
    "reference_piles = None\n",
    "\n",
    "try:\n",
    "    # Try to load CSV pile positions first (flat tabular format)\n",
    "    if reference_csv_path.exists():\n",
    "        reference_piles = pd.read_csv(reference_csv_path)\n",
    "        print(f\"✅ Loaded CSV reference data: {len(reference_piles)} reference piles\")\n",
    "        print(f\"📊 Source: {reference_csv_path}\")\n",
    "    elif reference_json_path.exists():\n",
    "        # Load JSON metadata and extract pile positions\n",
    "        with open(reference_json_path, 'r') as f:\n",
    "            ifc_metadata = json.load(f)\n",
    "        \n",
    "        # Extract pile data from JSON structure\n",
    "        if 'pile_elements' in ifc_metadata:\n",
    "            pile_data = []\n",
    "            for pile in ifc_metadata['pile_elements']:\n",
    "                pile_data.append({\n",
    "                    'Pile No.': pile.get('GlobalId', pile.get('Name', 'Unknown')),\n",
    "                    'x': pile.get('X_Local', pile.get('x', 0)),\n",
    "                    'y': pile.get('Y_Local', pile.get('y', 0)),\n",
    "                    'z': pile.get('Z_Local', pile.get('z', 0)),\n",
    "                    'Pile Type': pile.get('ObjectType', 'COLUMN')\n",
    "                })\n",
    "            reference_piles = pd.DataFrame(pile_data)\n",
    "            print(f\"✅ Loaded JSON reference data: {len(reference_piles)} reference piles\")\n",
    "            print(f\"📊 Source: {reference_json_path}\")\n",
    "        else:\n",
    "            raise ValueError(\"No pile_elements found in JSON metadata\")\n",
    "    else:\n",
    "        raise FileNotFoundError(\"No reference data found\")\n",
    "    \n",
    "    print(f\"📊 Columns: {list(reference_piles.columns)}\")\n",
    "    \n",
    "    # Check if we have the required coordinate columns\n",
    "    coord_cols = ['x', 'y', 'z']\n",
    "    if not all(col in reference_piles.columns for col in coord_cols):\n",
    "        print(f\"⚠️ Missing coordinate columns. Available: {list(reference_piles.columns)}\")\n",
    "        # Try alternative column names\n",
    "        alt_coord_cols = ['X_Local', 'Y_Local', 'Z_Local']\n",
    "        if all(col in reference_piles.columns for col in alt_coord_cols):\n",
    "            reference_piles = reference_piles.rename(columns={\n",
    "                'X_Local': 'x', 'Y_Local': 'y', 'Z_Local': 'z'\n",
    "            })\n",
    "            print(f\"✅ Renamed coordinate columns to standard format\")\n",
    "    \n",
    "    # Display sample reference data\n",
    "    print(\"\\n📋 Sample reference piles:\")\n",
    "    display(reference_piles.head())\n",
    "    \n",
    "except FileNotFoundError:\n",
    "    print(f\"❌ Reference data not found: {reference_data_path}\")\n",
    "    print(\"Trying alternative reference data sources...\")\n",
    "    \n",
    "    # Try CAD metadata\n",
    "    cad_data_path = data_path / 'cad_metadata.csv'\n",
    "    try:\n",
    "        reference_piles = pd.read_csv(cad_data_path)\n",
    "        print(f\"✅ Loaded CAD reference data: {len(reference_piles)} reference piles\")\n",
    "    except FileNotFoundError:\n",
    "        print(f\"❌ No reference data found. Creating synthetic reference data...\")\n",
    "        \n",
    "        # Create synthetic reference data for demonstration\n",
    "        reference_piles = pd.DataFrame({\n",
    "            'Pile No.': [f'PILE_{i+1}' for i in range(5)],\n",
    "            'x': [5.1, 9.8, 15.2, 7.9, 12.5],  # Slightly offset from detected\n",
    "            'y': [4.9, 8.1, 5.8, 12.2, 10.0],\n",
    "            'z': [0.0, 0.0, 0.0, 0.0, 0.0],  # Ground level\n",
    "            'Pile Type': ['COLUMN', 'COLUMN', 'COLUMN', 'COLUMN', 'COLUMN']\n",
    "        })\n",
    "        \n",
    "        print(f\"🔧 Created {len(reference_piles)} synthetic reference piles\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3️⃣ Spatial Matching\n",
    "\n",
    "Match detected piles with reference piles based on spatial proximity."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def match_detected_to_reference(detected_piles, reference_piles, max_distance=2.0):\n",
    "    \"\"\"\n",
    "    Match detected piles to reference piles based on spatial proximity.\n",
    "    \n",
    "    Parameters:\n",
    "    -----------\n",
    "    detected_piles : pandas.DataFrame\n",
    "        Detected pile locations\n",
    "    reference_piles : pandas.DataFrame\n",
    "        Reference pile locations\n",
    "    max_distance : float\n",
    "        Maximum distance for matching (meters)\n",
    "        \n",
    "    Returns:\n",
    "    --------\n",
    "    matches : list of dict\n",
    "        List of matched pile pairs with distances\n",
    "    validation_metrics : dict\n",
    "        Validation metrics and statistics\n",
    "    \"\"\"\n",
    "    \n",
    "    print(f\"🔍 Matching detected piles to reference piles...\")\n",
    "    print(f\"  Max matching distance: {max_distance}m\")\n",
    "    print(f\"  Detected piles: {len(detected_piles)}\")\n",
    "    print(f\"  Reference piles: {len(reference_piles)}\")\n",
    "    \n",
    "    # Extract coordinates\n",
    "    detected_coords = detected_piles[['x', 'y']].values\n",
    "    reference_coords = reference_piles[['x', 'y']].values\n",
    "    \n",
    "    # Calculate distance matrix\n",
    "    distance_matrix = cdist(detected_coords, reference_coords)\n",
    "    \n",
    "    # Find matches using greedy assignment\n",
    "    matches = []\n",
    "    used_reference = set()\n",
    "    used_detected = set()\n",
    "    \n",
    "    # Sort by distance to prioritize closest matches\n",
    "    flat_indices = np.unravel_index(np.argsort(distance_matrix.ravel()), distance_matrix.shape)\n",
    "    \n",
    "    for det_idx, ref_idx in zip(flat_indices[0], flat_indices[1]):\n",
    "        distance = distance_matrix[det_idx, ref_idx]\n",
    "        \n",
    "        # Check if within max distance and not already used\n",
    "        if (distance <= max_distance and \n",
    "            det_idx not in used_detected and \n",
    "            ref_idx not in used_reference):\n",
    "            \n",
    "            match = {\n",
    "                'detected_idx': int(det_idx),\n",
    "                'reference_idx': int(ref_idx),\n",
    "                'distance': float(distance),\n",
    "                'detected_pile': detected_piles.iloc[det_idx].to_dict(),\n",
    "                'reference_pile': reference_piles.iloc[ref_idx].to_dict()\n",
    "            }\n",
    "            \n",
    "            matches.append(match)\n",
    "            used_detected.add(det_idx)\n",
    "            used_reference.add(ref_idx)\n",
    "    \n",
    "    # Calculate validation metrics\n",
    "    true_positives = len(matches)\n",
    "    false_positives = len(detected_piles) - true_positives\n",
    "    false_negatives = len(reference_piles) - true_positives\n",
    "    \n",
    "    precision = true_positives / len(detected_piles) if len(detected_piles) > 0 else 0\n",
    "    recall = true_positives / len(reference_piles) if len(reference_piles) > 0 else 0\n",
    "    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0\n",
    "    \n",
    "    # Spatial accuracy metrics\n",
    "    distances = [match['distance'] for match in matches]\n",
    "    mean_distance_error = np.mean(distances) if distances else 0\n",
    "    rmse = np.sqrt(np.mean(np.array(distances)**2)) if distances else 0\n",
    "    \n",
    "    validation_metrics = {\n",
    "        'true_positives': true_positives,\n",
    "        'false_positives': false_positives,\n",
    "        'false_negatives': false_negatives,\n",
    "        'precision': precision,\n",
    "        'recall': recall,\n",
    "        'f1_score': f1_score,\n",
    "        'mean_distance_error': mean_distance_error,\n",
    "        'rmse': rmse,\n",
    "        'max_matching_distance': max_distance,\n",
    "        'total_matches': len(matches),\n",
    "        'unmatched_detected': list(set(range(len(detected_piles))) - used_detected),\n",
    "        'unmatched_reference': list(set(range(len(reference_piles))) - used_reference)\n",
    "    }\n",
    "    \n",
    "    print(f\"\\n✅ Matching complete:\")\n",
    "    print(f\"  Matches found: {true_positives}\")\n",
    "    print(f\"  Precision: {precision:.3f}\")\n",
    "    print(f\"  Recall: {recall:.3f}\")\n",
    "    print(f\"  F1-Score: {f1_score:.3f}\")\n",
    "    print(f\"  Mean distance error: {mean_distance_error:.3f}m\")\n",
    "    print(f\"  RMSE: {rmse:.3f}m\")\n",
    "    \n",
    "    return matches, validation_metrics"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Perform spatial matching\n",
    "if detected_piles is not None and reference_piles is not None:\n",
    "    \n",
    "    # Matching parameters\n",
    "    MAX_MATCHING_DISTANCE = 2.0  # meters - adjust based on expected accuracy\n",
    "    \n",
    "    matches, validation_metrics = match_detected_to_reference(\n",
    "        detected_piles=detected_piles,\n",
    "        reference_piles=reference_piles,\n",
    "        max_distance=MAX_MATCHING_DISTANCE\n",
    "    )\n",
    "    \n",
    "    print(f\"\\n✅ Validation complete! Results saved for further analysis.\")\n",
    "    \n",
    "else:\n",
    "    print(\"❌ Cannot perform matching - missing detected piles or reference data.\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4️⃣ Export Validation Results\n",
    "\n",
    "Export validation results in the specified formats for compliance analysis."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Export validation results\n",
    "if 'matches' in locals() and 'validation_metrics' in locals():\n",
    "    \n",
    "    # Export validation analysis as CSV (sortable analysis, one row per pile)\n",
    "    if matches:\n",
    "        validation_data = []\n",
    "        \n",
    "        for match in matches:\n",
    "            det_pile = match['detected_pile']\n",
    "            ref_pile = match['reference_pile']\n",
    "            \n",
    "            # Calculate deviations\n",
    "            x_deviation = det_pile['x'] - ref_pile['x']\n",
    "            y_deviation = det_pile['y'] - ref_pile['y']\n",
    "            z_deviation = det_pile['z'] - ref_pile['z']\n",
    "            spatial_deviation = match['distance']\n",
    "            \n",
    "            # Compliance flags\n",
    "            spatial_compliant = spatial_deviation <= 1.0  # 1m tolerance\n",
    "            type_compliant = det_pile.get('pile_type', '').lower() == ref_pile.get('Pile Type', '').lower()\n",
    "            \n",
    "            validation_data.append({\n",
    "                'pile_id': det_pile['pile_id'],\n",
    "                'reference_id': ref_pile.get('Pile No.', f\"REF_{match['reference_idx']}\"),\n",
    "                'detected_x': round(det_pile['x'], 3),\n",
    "                'detected_y': round(det_pile['y'], 3),\n",
    "                'detected_z': round(det_pile['z'], 3),\n",
    "                'reference_x': round(ref_pile['x'], 3),\n",
    "                'reference_y': round(ref_pile['y'], 3),\n",
    "                'reference_z': round(ref_pile['z'], 3),\n",
    "                'x_deviation': round(x_deviation, 3),\n",
    "                'y_deviation': round(y_deviation, 3),\n",
    "                'z_deviation': round(z_deviation, 3),\n",
    "                'spatial_deviation': round(spatial_deviation, 3),\n",
    "                'detected_type': det_pile.get('pile_type', 'unknown'),\n",
    "                'reference_type': ref_pile.get('Pile Type', 'unknown'),\n",
    "                'confidence': det_pile.get('confidence', 0),\n",
    "                'spatial_compliant': spatial_compliant,\n",
    "                'type_compliant': type_compliant,\n",
    "                'overall_compliant': spatial_compliant and type_compliant,\n",
    "                'validation_timestamp': datetime.now().isoformat()\n",
    "            })\n",
    "        \n",
    "        validation_df = pd.DataFrame(validation_data)\n",
    "        \n",
    "        # Sort by spatial deviation (best matches first)\n",
    "        validation_df = validation_df.sort_values('spatial_deviation', na_last=True).reset_index(drop=True)\n",
    "        \n",
    "        csv_path = validation_path / f\"{PROJECT_NAME}_pile_validation_analysis.csv\"\n",
    "        validation_df.to_csv(csv_path, index=False)\n",
    "        print(f\"💾 Saved validation analysis: {csv_path}\")\n",
    "        print(f\"   Format: CSV with sortable analysis (one row per pile)\")\n",
    "        print(f\"   Records: {len(validation_df)} pile validations\")\n",
    "        \n",
    "        # Export validation metadata as JSON (nested deviations per pile)\n",
    "        validation_metadata = {\n",
    "            'project_name': PROJECT_NAME,\n",
    "            'stage': 'pile_validation',\n",
    "            'validation_timestamp': datetime.now().isoformat(),\n",
    "            'validation_method': 'spatial_proximity_matching',\n",
    "            'overall_metrics': validation_metrics,\n",
    "            'pile_deviations': {},\n",
    "            'compliance_summary': {\n",
    "                'total_validated': len(validation_df),\n",
    "                'spatially_compliant': int(validation_df['spatial_compliant'].sum()),\n",
    "                'type_compliant': int(validation_df['type_compliant'].sum()),\n",
    "                'overall_compliant': int(validation_df['overall_compliant'].sum())\n",
    "            }\n",
    "        }\n",
    "        \n",
    "        # Add nested deviations per pile\n",
    "        for _, row in validation_df.iterrows():\n",
    "            pile_id = row['pile_id']\n",
    "            validation_metadata['pile_deviations'][pile_id] = {\n",
    "                'spatial_deviation': {\n",
    "                    'x_deviation': row['x_deviation'],\n",
    "                    'y_deviation': row['y_deviation'],\n",
    "                    'z_deviation': row['z_deviation'],\n",
    "                    'total_distance': row['spatial_deviation']\n",
    "                },\n",
    "                'type_deviation': {\n",
    "                    'detected_type': row['detected_type'],\n",
    "                    'reference_type': row['reference_type'],\n",
    "                    'type_match': row['type_compliant']\n",
    "                },\n",
    "                'compliance_flags': {\n",
    "                    'spatial_compliant': row['spatial_compliant'],\n",
    "                    'type_compliant': row['type_compliant'],\n",
    "                    'overall_compliant': row['overall_compliant']\n",
    "                },\n",
    "                'confidence_score': row['confidence']\n",
    "            }\n",
    "        \n",
    "        json_path = validation_path / f\"{PROJECT_NAME}_pile_validation_metadata.json\"\n",
    "        with open(json_path, 'w') as f:\n",
    "            json.dump(validation_metadata, f, indent=2)\n",
    "        print(f\"💾 Saved validation metadata: {json_path}\")\n",
    "        print(f\"   Format: JSON with nested deviations per pile\")\n",
    "        \n",
    "        print(f\"\\n✅ Validation export complete! Files saved to: {validation_path}\")\n",
    "        \n",
    "    else:\n",
    "        print(\"❌ No matches found to export.\")\n",
    "        \n",
    "else:\n",
    "    print(\"❌ No validation results to export. Please run validation first.\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📝 Summary\n",
    "\n",
    "This notebook provided comprehensive validation of pile detection results:\n",
    "\n",
    "### ✅ **What We Accomplished:**\n",
    "1. **Loaded Detection Results** from foundation_pile_detection.ipynb\n",
    "2. **Loaded Reference Data** (IFC metadata, CAD data, or manual annotations)\n",
    "3. **Performed Spatial Matching** between detected and reference piles\n",
    "4. **Calculated Validation Metrics** (precision, recall, F1-score, spatial accuracy)\n",
    "5. **Analyzed Errors** (false positives, false negatives, spatial errors)\n",
    "6. **Generated Reports** with recommendations for improvement\n",
    "\n",
    "### 🎯 **Key Validation Metrics:**\n",
    "- **Detection Accuracy**: How well the algorithm identifies piles\n",
    "- **Spatial Accuracy**: How precisely pile locations are determined\n",
    "- **Completeness**: Percentage of reference piles successfully detected\n",
    "- **Quality Assessment**: Overall performance grading\n",
    "\n",
    "### 🔄 **Workflow Integration:**\n",
    "This validation workflow integrates with:\n",
    "- **foundation_pile_detection.ipynb**: Provides detection results to validate\n",
    "- **auto_label_from_ifc.ipynb**: Can use IFC data as reference ground truth\n",
    "- **cad_rule_based_detection.ipynb**: Can validate rule-based detection results\n",
    "\n",
    "### ⚙️ **Parameter Tuning Guidelines:**\n",
    "\n",
    "#### **Improving Precision** (Reduce False Positives):\n",
    "- Increase minimum cluster size in detection\n",
    "- Tighten height filtering parameters\n",
    "- Add more geometric constraints\n",
    "\n",
    "#### **Improving Recall** (Reduce False Negatives):\n",
    "- Decrease minimum cluster size\n",
    "- Relax height filtering parameters\n",
    "- Reduce DBSCAN epsilon parameter\n",
    "\n",
    "#### **Improving Spatial Accuracy**:\n",
    "- Improve point cloud alignment quality\n",
    "- Use higher resolution point clouds\n",
    "- Refine ground level estimation\n",
    "\n",
    "**📧 Contact**: For questions about validation methodology, reach out to the development team."
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",\n",
   "language": "python",\n",
   "name": "python3"\n",
  },\n",
  "language_info": {\n",
   "codemirror_mode": {\n",
    "name": "ipython",\n",
    "version": 3\n",
   },\n",
   "file_extension": ".py",\n",
   "mimetype": "text/x-python",\n",
   "name": "python",\n",
   "nbconvert_exporter": "python",\n",
   "pygments_lexer": "ipython3",\n",
   "version": "3.8.5"\n",
  }\n",
 },\n",
 "nbformat": 4,\n",
 "nbformat_minor": 4\n",
}
