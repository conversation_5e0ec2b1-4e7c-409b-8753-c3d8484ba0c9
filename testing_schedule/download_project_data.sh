#!/bin/bash

# =============================================================================
# Energy Inspection 3D - Project Data Download Script
# =============================================================================
# This script downloads all project data using rclone and direct downloads
# Supports S3 buckets, HTTPS URLs, and Google Drive backup
# =============================================================================

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# =============================================================================
# CONFIGURATION AND SETUP
# =============================================================================

# Load environment variables
if [[ -f ".env" ]]; then
    source .env
    echo "✅ Loaded configuration from .env"
else
    echo "❌ .env file not found. Please create it first."
    exit 1
fi

# Set defaults if not specified in .env
GDRIVE_REMOTE=${GDRIVE_REMOTE:-"gdrive"}
GDRIVE_BASE_DIR=${GDRIVE_BASE_DIR:-"energy-inspection-3d-data"}
LOCAL_DATA_DIR=${LOCAL_DATA_DIR:-"./data"}
LOG_FILE=${LOG_FILE:-"./logs/data_download.log"}
PARALLEL_DOWNLOADS=${PARALLEL_DOWNLOADS:-4}
SKIP_EXISTING=${SKIP_EXISTING:-"true"}
DRY_RUN=${DRY_RUN:-"false"}
VERBOSE=${VERBOSE:-"false"}

# Create directories
mkdir -p "$(dirname "$LOG_FILE")"
mkdir -p "$LOCAL_DATA_DIR"
mkdir -p logs

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_debug() { [[ "$VERBOSE" == "true" ]] && log "DEBUG" "$@" || true; }

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

check_dependencies() {
    log_info "Checking dependencies..."
    
    local deps=("rclone" "wget" "curl")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_error "$dep is not installed. Please install it first."
            exit 1
        fi
    done
    
    # Check rclone configuration
    if ! rclone listremotes | grep -q "^${GDRIVE_REMOTE}:$"; then
        log_warn "Google Drive remote '$GDRIVE_REMOTE' not found in rclone config"
        log_info "Please run: rclone config"
    fi
    
    log_info "✅ All dependencies checked"
}

create_directory_structure() {
    log_info "Creating directory structure..."
    
    local projects=("Castro" "Mudjar" "Giorgio" "McCarthy" "RPCS" "RES")
    local types=("ENEL" "USA")
    
    for project in "${projects[@]}"; do
        if [[ "$project" =~ ^(Castro|Mudjar|Giorgio)$ ]]; then
            project_type="ENEL"
        else
            project_type="USA"
        fi
        
        mkdir -p "$LOCAL_DATA_DIR/$project_type/$project/raw"
        mkdir -p "$LOCAL_DATA_DIR/$project_type/$project/pointcloud"
        mkdir -p "$LOCAL_DATA_DIR/$project_type/$project/cad"
        mkdir -p "$LOCAL_DATA_DIR/$project_type/$project/ortho"
    done
    
    log_info "✅ Directory structure created"
}

# =============================================================================
# DOWNLOAD FUNCTIONS
# =============================================================================

download_s3_with_rclone() {
    local s3_url=$1
    local local_path=$2
    local description=$3
    
    log_info "Downloading S3: $description"
    log_debug "S3 URL: $s3_url"
    log_debug "Local path: $local_path"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would download: $s3_url -> $local_path"
        return 0
    fi
    
    # Convert S3 URL to rclone format
    local bucket_and_path=${s3_url#s3://}
    local bucket=${bucket_and_path%%/*}
    local path=${bucket_and_path#*/}
    
    # Create S3 remote configuration for this bucket
    local s3_remote="s3-$bucket"
    
    # Configure S3 remote if it doesn't exist
    if ! rclone listremotes | grep -q "^${s3_remote}:$"; then
        log_info "Configuring S3 remote for bucket: $bucket"
        
        # Create temporary rclone config
        rclone config create "$s3_remote" s3 \
            provider=AWS \
            region="${AWS_REGION:-us-east-1}" \
            acl=private \
            --non-interactive
    fi
    
    # Download with rclone
    local rclone_opts=(
        --transfers="${RCLONE_TRANSFERS:-4}"
        --checkers="${RCLONE_CHECKERS:-8}"
        --buffer-size="${RCLONE_BUFFER_SIZE:-16M}"
    )
    
    if [[ "$SKIP_EXISTING" == "true" ]]; then
        # Use --ignore-existing for older rclone versions
        rclone_opts+=(--ignore-existing)
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        rclone_opts+=(--progress --verbose)
    fi
    
    if rclone copy "${s3_remote}:${path}" "$local_path" "${rclone_opts[@]}"; then
        log_info "✅ Successfully downloaded: $description"
        return 0
    else
        log_error "❌ Failed to download: $description"
        return 1
    fi
}

download_https() {
    local url=$1
    local local_path=$2
    local description=$3
    
    log_info "Downloading HTTPS: $description"
    log_debug "URL: $url"
    log_debug "Local path: $local_path"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would download: $url -> $local_path"
        return 0
    fi
    
    # Extract filename from URL
    local filename=$(basename "$url")
    local full_local_path="$local_path/$filename"
    
    # Skip if file exists and SKIP_EXISTING is true
    if [[ "$SKIP_EXISTING" == "true" && -f "$full_local_path" ]]; then
        log_info "⏭️  Skipping existing file: $filename"
        return 0
    fi
    
    # Download with wget
    local wget_opts=(
        --timeout="${CONNECTION_TIMEOUT:-30}"
        --tries="${MAX_RETRIES:-3}"
        --continue
        --progress=bar
    )
    
    if [[ "$VERBOSE" != "true" ]]; then
        wget_opts+=(--quiet)
    fi
    
    if wget "${wget_opts[@]}" -P "$local_path" "$url"; then
        log_info "✅ Successfully downloaded: $description"
        return 0
    else
        log_error "❌ Failed to download: $description"
        return 1
    fi
}

backup_to_gdrive() {
    local local_path=$1
    local gdrive_path=$2
    local description=$3
    
    if [[ "$BACKUP_TO_GDRIVE" != "true" ]]; then
        return 0
    fi
    
    log_info "Backing up to Google Drive: $description"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would backup: $local_path -> $GDRIVE_REMOTE:$gdrive_path"
        return 0
    fi
    
    local rclone_opts=(
        --transfers="${RCLONE_TRANSFERS:-4}"
        --checkers="${RCLONE_CHECKERS:-8}"
    )
    
    if [[ "$SKIP_EXISTING" == "true" ]]; then
        # Use --ignore-existing for older rclone versions
        rclone_opts+=(--ignore-existing)
    fi
    
    if rclone copy "$local_path" "$GDRIVE_REMOTE:$GDRIVE_BASE_DIR/$gdrive_path" "${rclone_opts[@]}"; then
        log_info "✅ Successfully backed up: $description"
        return 0
    else
        log_error "❌ Failed to backup: $description"
        return 1
    fi
}

# =============================================================================
# PROJECT-SPECIFIC DOWNLOAD FUNCTIONS
# =============================================================================

download_castro() {
    log_info "=== Downloading Castro (ENEL) Project ==="
    
    local project_dir="$LOCAL_DATA_DIR/ENEL/Castro"
    
    # Point Cloud
    download_s3_with_rclone \
        "s3://preetam-filezilla-test/Castro/Pointcloud/" \
        "$project_dir/pointcloud" \
        "Castro Point Cloud"
    
    # CAD Files
    download_s3_with_rclone \
        "s3://ftp-enel/montalto_di_castro-italy/2025-01-30/CAD/" \
        "$project_dir/cad" \
        "Castro CAD Files"
    
    # Ortho Images
    download_s3_with_rclone \
        "s3://preetam-filezilla-test/Castro/Ortho/" \
        "$project_dir/ortho" \
        "Castro Ortho Images"
    
    # Backup to Google Drive
    backup_to_gdrive "$project_dir" "ENEL/Castro" "Castro Project"
}

download_mudjar() {
    log_info "=== Downloading Mudjar (ENEL) Project ==="
    
    local project_dir="$LOCAL_DATA_DIR/ENEL/Mudjar"
    
    # Point Cloud (HTTPS)
    download_https \
        "https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las" \
        "$project_dir/pointcloud" \
        "Mudjar Point Cloud"
    
    # CAD Files
    download_s3_with_rclone \
        "s3://ftp-enel/mudejar-spain/" \
        "$project_dir/cad" \
        "Mudjar CAD Files"
    
    # Note: Ortho URL appears to be the same as point cloud - this might be an error in the data
    log_warn "Mudjar ortho URL appears to be same as point cloud - skipping ortho download"
    
    # Backup to Google Drive
    backup_to_gdrive "$project_dir" "ENEL/Mudjar" "Mudjar Project"
}

download_giorgio() {
    log_info "=== Downloading Giorgio (ENEL) Project ==="
    
    local project_dir="$LOCAL_DATA_DIR/ENEL/Giorgio"
    
    # Point Cloud (HTTPS)
    download_https \
        "https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Paini_Di_Di_Giorgio/Flight12/Giorgio_Fly12_pointcloud.las" \
        "$project_dir/pointcloud" \
        "Giorgio Point Cloud"
    
    # CAD Files (multiple locations)
    download_s3_with_rclone \
        "s3://ftp-enel/pian_di_di_giorgio-italy/2024-05-15/" \
        "$project_dir/cad/2024-05-15" \
        "Giorgio CAD Files (2024-05-15)"
    
    download_s3_with_rclone \
        "s3://ftp-enel/pian_di_di_giorgio-italy/" \
        "$project_dir/cad/main" \
        "Giorgio CAD Files (Main)"
    
    # Ortho Images (HTTPS)
    download_https \
        "https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Paini_Di_Di_Giorgio/Flight12/Giorgio_Fly12_Ortho.tif" \
        "$project_dir/ortho" \
        "Giorgio Ortho Images"
    
    # Backup to Google Drive
    backup_to_gdrive "$project_dir" "ENEL/Giorgio" "Giorgio Project"
}

download_mccarthy() {
    log_info "=== Downloading McCarthy (USA) Project ==="
    
    local project_dir="$LOCAL_DATA_DIR/USA/McCarthy"
    
    # Point Cloud
    download_s3_with_rclone \
        "s3://preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/" \
        "$project_dir/pointcloud" \
        "McCarthy Point Cloud"
    
    # CAD Files
    download_s3_with_rclone \
        "s3://ftp-mccarthy/CAD Files/" \
        "$project_dir/cad" \
        "McCarthy CAD Files"
    
    # Ortho Images
    download_s3_with_rclone \
        "s3://preetam-filezilla-test/McCarthy_Fly2/RGB_Ortho/" \
        "$project_dir/ortho" \
        "McCarthy Ortho Images"
    
    # Backup to Google Drive
    backup_to_gdrive "$project_dir" "USA/McCarthy" "McCarthy Project"
}

download_rpcs() {
    log_info "=== Downloading RPCS (USA) Project ==="
    
    local project_dir="$LOCAL_DATA_DIR/USA/RPCS"
    
    # Point Cloud
    download_s3_with_rclone \
        "s3://preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/" \
        "$project_dir/pointcloud" \
        "RPCS Point Cloud"
    
    # CAD Files
    download_s3_with_rclone \
        "s3://ftp-rpcs/Althea/CAD Files/" \
        "$project_dir/cad" \
        "RPCS CAD Files"
    
    # Ortho Images
    download_s3_with_rclone \
        "s3://preetam-filezilla-test/RCPS/Updated_031024/Ortho/" \
        "$project_dir/ortho" \
        "RPCS Ortho Images"
    
    # Backup to Google Drive
    backup_to_gdrive "$project_dir" "USA/RPCS" "RPCS Project"
}

download_res() {
    log_info "=== Downloading RES (USA) Project ==="
    
    local project_dir="$LOCAL_DATA_DIR/USA/RES"
    
    # Point Cloud
    download_s3_with_rclone \
        "s3://ftp-upload-images/Data files to GIS Team/CPM & CQM/2025/RES/Block11/Pointcloud/" \
        "$project_dir/pointcloud" \
        "RES Point Cloud"
    
    # CAD Files
    download_s3_with_rclone \
        "s3://preetam-filezilla-test/RES_Renewable/BLOCK_11/CAD/" \
        "$project_dir/cad" \
        "RES CAD Files"
    
    # Ortho Images (HTTPS)
    download_https \
        "https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2025/RES/Block11/RSE_Block11_ortho.tif" \
        "$project_dir/ortho" \
        "RES Ortho Images"
    
    # Backup to Google Drive
    backup_to_gdrive "$project_dir" "USA/RES" "RES Project"
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    local start_time=$(date +%s)
    
    log_info "🚀 Starting Energy Inspection 3D data download"
    log_info "Configuration: DRY_RUN=$DRY_RUN, SKIP_EXISTING=$SKIP_EXISTING"
    
    # Setup
    check_dependencies
    create_directory_structure
    
    # Download projects based on priority
    local failed_downloads=()
    
    # High Priority (ENEL Projects)
    log_info "📥 Downloading High Priority Projects (ENEL)"
    download_castro || failed_downloads+=("Castro")
    download_mudjar || failed_downloads+=("Mudjar")
    download_giorgio || failed_downloads+=("Giorgio")
    
    # Medium Priority (USA Projects)
    log_info "📥 Downloading Medium Priority Projects (USA)"
    download_mccarthy || failed_downloads+=("McCarthy")
    download_rpcs || failed_downloads+=("RPCS")
    
    # Low Priority
    log_info "📥 Downloading Low Priority Projects"
    download_res || failed_downloads+=("RES")
    
    # Summary
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_info "⏱️  Total download time: ${duration}s"
    
    if [[ ${#failed_downloads[@]} -eq 0 ]]; then
        log_info "🎉 All downloads completed successfully!"
    else
        log_error "❌ Failed downloads: ${failed_downloads[*]}"
        exit 1
    fi
    
    # Generate summary report
    generate_download_summary
}

generate_download_summary() {
    local summary_file="logs/download_summary_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "Energy Inspection 3D - Download Summary"
        echo "======================================="
        echo "Date: $(date)"
        echo "Configuration: DRY_RUN=$DRY_RUN, SKIP_EXISTING=$SKIP_EXISTING"
        echo ""
        echo "Directory Structure:"
        find "$LOCAL_DATA_DIR" -type d | sort
        echo ""
        echo "Downloaded Files:"
        find "$LOCAL_DATA_DIR" -type f -exec ls -lh {} \; | sort
        echo ""
        echo "Total Size:"
        du -sh "$LOCAL_DATA_DIR"
    } > "$summary_file"
    
    log_info "📋 Download summary saved to: $summary_file"
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Handle command line arguments
case "${1:-all}" in
    "castro"|"Castro")
        check_dependencies && create_directory_structure && download_castro
        ;;
    "mudjar"|"Mudjar")
        check_dependencies && create_directory_structure && download_mudjar
        ;;
    "giorgio"|"Giorgio")
        check_dependencies && create_directory_structure && download_giorgio
        ;;
    "mccarthy"|"McCarthy")
        check_dependencies && create_directory_structure && download_mccarthy
        ;;
    "rpcs"|"RPCS")
        check_dependencies && create_directory_structure && download_rpcs
        ;;
    "res"|"RES")
        check_dependencies && create_directory_structure && download_res
        ;;
    "enel"|"ENEL")
        check_dependencies && create_directory_structure
        download_castro && download_mudjar && download_giorgio
        ;;
    "usa"|"USA")
        check_dependencies && create_directory_structure
        download_mccarthy && download_rpcs && download_res
        ;;
    "all"|*)
        main
        ;;
esac

log_info "✅ Script execution completed"

# =============================================================================
# USAGE EXAMPLES
# =============================================================================

# Download all projects:
# ./download_project_data.sh

# Download specific project:
# ./download_project_data.sh castro
# ./download_project_data.sh mudjar
# ./download_project_data.sh giorgio
# ./download_project_data.sh mccarthy
# ./download_project_data.sh rpcs
# ./download_project_data.sh res

# Download by region:
# ./download_project_data.sh enel
# ./download_project_data.sh usa

# Dry run (show what would be downloaded):
# DRY_RUN=true ./download_project_data.sh

# Verbose output:
# VERBOSE=true ./download_project_data.sh
