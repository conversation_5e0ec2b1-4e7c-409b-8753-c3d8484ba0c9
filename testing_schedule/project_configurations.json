{"testing_schedule": {"start_date": "2025-01-01", "duration_days": 7, "daily_hours": 8, "team_size": 2}, "projects": {"Castro": {"project_type": "ENEL", "project_name": "<PERSON>", "full_name": "<PERSON><PERSON><PERSON> - ENEL", "priority": "High", "data_sources": {"point_cloud": {"url": "s3://preetam-filezilla-test/Castro/Pointcloud/", "type": "s3", "format": "las", "estimated_size_gb": 2.5}, "ifc": {"available": false}, "cad": {"url": "s3://ftp-enel/montalto_di_castro-italy/2025-01-30/CAD/", "type": "s3", "format": "dwg", "estimated_size_gb": 0.1}, "ortho": {"url": "s3://preetam-filezilla-test/Castro/Ortho/", "type": "s3", "format": "tif", "estimated_size_gb": 1.0}}, "testing_schedule": {"day": 1, "stages": ["ground_segmentation"], "expected_duration_hours": 4}, "validation_criteria": {"ground_segmentation": {"min_ground_ratio": 0.3, "max_ground_ratio": 0.7, "min_points": 100000}, "pile_detection": {"expected_pile_count_range": [10, 50], "min_confidence": 0.5}}}, "Mudjar": {"project_type": "ENEL", "project_name": "<PERSON><PERSON><PERSON>", "full_name": "Mudjar - ENEL", "priority": "High", "data_sources": {"point_cloud": {"url": "https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las", "type": "https", "format": "las", "estimated_size_gb": 3.2}, "ifc": {"available": false}, "cad": {"url": "s3://ftp-enel/mudejar-spain/", "type": "s3", "format": "dwg", "estimated_size_gb": 0.2}, "ortho": {"url": "https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las", "type": "https", "format": "tif", "estimated_size_gb": 1.5}}, "testing_schedule": {"day": 1, "stages": ["ground_segmentation"], "expected_duration_hours": 4}, "validation_criteria": {"ground_segmentation": {"min_ground_ratio": 0.25, "max_ground_ratio": 0.65, "min_points": 150000}, "pile_detection": {"expected_pile_count_range": [15, 60], "min_confidence": 0.5}}}, "Giorgio": {"project_type": "ENEL", "project_name": "<PERSON>", "full_name": "<PERSON><PERSON> - ENEL", "priority": "High", "data_sources": {"point_cloud": {"url": "https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>/Flight12/<PERSON>_Fly12_pointcloud.las", "type": "https", "format": "las", "estimated_size_gb": 2.8}, "ifc": {"available": false}, "cad": {"url": "s3://ftp-enel/pian_di_di_giorgio-italy/2024-05-15/", "type": "s3", "format": "dwg", "estimated_size_gb": 0.15}, "ortho": {"url": "https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>/Flight12/<PERSON>_Fly12_Ortho.tif", "type": "https", "format": "tif", "estimated_size_gb": 1.2}}, "testing_schedule": {"day": 3, "stages": ["ground_segmentation", "pile_detection"], "expected_duration_hours": 6}, "validation_criteria": {"ground_segmentation": {"min_ground_ratio": 0.35, "max_ground_ratio": 0.75, "min_points": 120000}, "pile_detection": {"expected_pile_count_range": [12, 45], "min_confidence": 0.5}}}, "McCarthy": {"project_type": "USA", "project_name": "<PERSON>", "full_name": "Sunstreams Project - McCarthy", "priority": "Medium", "data_sources": {"point_cloud": {"url": "s3://preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/", "type": "s3", "format": "las", "estimated_size_gb": 4.1}, "ifc": {"available": false}, "cad": {"url": "s3://ftp-mccarthy/CAD Files/", "type": "s3", "format": "dwg", "estimated_size_gb": 0.3}, "ortho": {"url": "s3://preetam-filezilla-test/McCarthy_Fly2/RGB_Ortho/", "type": "s3", "format": "tif", "estimated_size_gb": 2.0}}, "testing_schedule": {"day": 4, "stages": ["ground_segmentation", "alignment", "pile_detection"], "expected_duration_hours": 8}, "validation_criteria": {"ground_segmentation": {"min_ground_ratio": 0.4, "max_ground_ratio": 0.8, "min_points": 200000}, "pile_detection": {"expected_pile_count_range": [20, 80], "min_confidence": 0.5}}}, "RPCS": {"project_type": "USA", "project_name": "RPCS", "full_name": "Althea - RPCS", "priority": "Medium", "data_sources": {"point_cloud": {"url": "s3://preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/", "type": "s3", "format": "las", "estimated_size_gb": 3.5}, "ifc": {"available": false}, "cad": {"url": "s3://ftp-rpcs/Althea/CAD Files/", "type": "s3", "format": "dwg", "estimated_size_gb": 0.25}, "ortho": {"url": "s3://preetam-filezilla-test/RCPS/Updated_031024/Ortho/", "type": "s3", "format": "tif", "estimated_size_gb": 1.8}}, "testing_schedule": {"day": 4, "stages": ["ground_segmentation", "alignment", "pile_detection"], "expected_duration_hours": 8}, "validation_criteria": {"ground_segmentation": {"min_ground_ratio": 0.35, "max_ground_ratio": 0.75, "min_points": 180000}, "pile_detection": {"expected_pile_count_range": [18, 70], "min_confidence": 0.5}}}, "RES": {"project_type": "USA", "project_name": "RES", "full_name": "Nortan - RES Renewables", "priority": "Low", "data_sources": {"point_cloud": {"url": "s3://ftp-upload-images/Data files to GIS Team/CPM & CQM/2025/RES/Block11/Pointcloud/", "type": "s3", "format": "las", "estimated_size_gb": 5.2}, "ifc": {"available": false}, "cad": {"url": "s3://preetam-filezilla-test/RES_Renewable/BLOCK_11/CAD/", "type": "s3", "format": "dwg", "estimated_size_gb": 0.4}, "ortho": {"url": "https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2025/RES/Block11/RSE_Block11_ortho.tif", "type": "https", "format": "tif", "estimated_size_gb": 2.5}}, "testing_schedule": {"day": 6, "stages": ["ground_segmentation", "alignment", "pile_detection", "trench_segmentation", "compliance_analysis"], "expected_duration_hours": 8}, "validation_criteria": {"ground_segmentation": {"min_ground_ratio": 0.3, "max_ground_ratio": 0.7, "min_points": 250000}, "pile_detection": {"expected_pile_count_range": [25, 100], "min_confidence": 0.5}}}}, "pipeline_stages": {"ground_segmentation": {"notebook": "notebooks/ground_segmentation/ground_removal.ipynb", "input_formats": ["las", "laz", "pcd"], "output_formats": ["ply", "pcd"], "expected_duration_minutes": [5, 15], "memory_requirements_gb": [2, 8]}, "alignment": {"notebook": "notebooks/alignment/ifc_pc_alignment_research.ipynb", "input_formats": ["ply", "pcd", "dwg", "ifc"], "output_formats": ["pcd"], "expected_duration_minutes": [10, 30], "memory_requirements_gb": [4, 12]}, "pile_detection": {"notebook": "notebooks/pile_detection/foundation_pile_detection.ipynb", "input_formats": ["ply", "pcd"], "output_formats": ["csv", "json"], "expected_duration_minutes": [5, 20], "memory_requirements_gb": [2, 6]}, "trench_segmentation": {"notebook": "notebooks/trench_detection/trench_segmentation_unet.ipynb", "input_formats": ["ply", "pcd", "tif"], "output_formats": ["ply", "npy"], "expected_duration_minutes": [10, 25], "memory_requirements_gb": [3, 10]}, "compliance_analysis": {"notebook": "notebooks/compliance_analysis/compliance_analysis.ipynb", "input_formats": ["csv", "json", "dwg"], "output_formats": ["csv", "json"], "expected_duration_minutes": [2, 10], "memory_requirements_gb": [1, 4]}, "visualization": {"notebook": "notebooks/visualization/pipeline_visualization.ipynb", "input_formats": ["ply", "pcd", "csv", "json"], "output_formats": ["html", "png", "npy"], "expected_duration_minutes": [5, 15], "memory_requirements_gb": [2, 8]}}, "system_requirements": {"minimum": {"ram_gb": 16, "storage_gb": 100, "cpu_cores": 4}, "recommended": {"ram_gb": 32, "storage_gb": 500, "cpu_cores": 8}}, "testing_environment": {"python_version": "3.8+", "required_packages": ["numpy", "pandas", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plotly", "open3d", "scikit-learn", "scipy", "laspy", "rasterio", "jup<PERSON><PERSON>"], "optional_packages": ["tensorflow", "torch", "pdal"]}}