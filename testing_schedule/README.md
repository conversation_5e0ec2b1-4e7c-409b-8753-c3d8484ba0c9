# 🧪 Pipeline Testing Suite

## 📋 **Quick Start**

### **Prerequisites**
```bash
# Install required packages
pip install numpy pandas matplotlib plotly open3d scikit-learn scipy laspy rasterio jupyter

# Install AWS CLI (for S3 data access)
pip install awscli
aws configure

# Make testing script executable
chmod +x run_pipeline_tests.py
```

### **Run Complete 7-Day Testing**
```bash
# Download all data first (recommended)
python run_pipeline_tests.py --download-only

# Run full 7-day testing schedule
python run_pipeline_tests.py
```

### **Run Specific Day**
```bash
# Run Day 1 (Castro & Mudjar ground segmentation)
python run_pipeline_tests.py --day 1

# Run Day 4 (USA projects)
python run_pipeline_tests.py --day 4
```

### **Run Specific Project & Stage**
```bash
# Test Castro ground segmentation only
python run_pipeline_tests.py --project Castro --stage ground_segmentation

# Test McCarthy pile detection only
python run_pipeline_tests.py --project McCarthy --stage pile_detection
```

---

## 📊 **Testing Schedule Overview**

| Day | Projects | Stages | Focus |
|-----|----------|--------|-------|
| **1** | Castro, Mudjar | Ground Segmentation | ENEL data validation |
| **2** | Castro, Mudjar | Alignment | CAD integration testing |
| **3** | Giorgio | Ground Seg + Pile Detection | Complete ENEL workflow |
| **4** | McCarthy, RPCS | Full Pipeline | USA projects validation |
| **5** | All Projects | Trench + Compliance | Advanced analysis stages |
| **6** | RES | Full Pipeline + Visualization | Complete workflow testing |
| **7** | All Projects | Integration + Documentation | Final validation |

---

## 📁 **File Structure**

```
testing_schedule/
├── 7_day_pipeline_testing_plan.md     # Detailed testing plan
├── project_configurations.json        # Project data configurations
├── run_pipeline_tests.py             # Automation script
├── README.md                          # This file
└── logs/                              # Generated during testing
    ├── pipeline_testing.log           # Main testing log
    └── day_X_results.json             # Daily results summary
```

---

## 🎯 **Project Data Summary**

### **ENEL Projects (High Priority)**
| Project | Point Cloud | CAD | Ortho | Size (GB) |
|---------|-------------|-----|-------|-----------|
| **Castro** | S3 | S3 | S3 | ~3.6 |
| **Mudjar** | HTTPS | S3 | HTTPS | ~4.9 |
| **Giorgio** | HTTPS | S3 | HTTPS | ~4.15 |

### **USA Projects (Medium Priority)**
| Project | Point Cloud | CAD | Ortho | Size (GB) |
|---------|-------------|-----|-------|-----------|
| **McCarthy** | S3 | S3 | S3 | ~6.4 |
| **RPCS** | S3 | S3 | S3 | ~5.55 |
| **RES** | S3 | S3 | HTTPS | ~8.1 |

**Total Data Size**: ~32.7 GB

---

## 🔧 **Manual Testing Commands**

### **Day 1: Ground Segmentation**
```bash
# Setup
mkdir -p data/ENEL/{Castro,Mudjar}/raw
mkdir -p output/ENEL/{Castro,Mudjar}/ground_segmentation

# Download Castro data
aws s3 sync s3://preetam-filezilla-test/Castro/Pointcloud/ data/ENEL/Castro/raw/

# Download Mudjar data
wget -P data/ENEL/Mudjar/raw/ "https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las"

# Run ground segmentation
cd notebooks/ground_segmentation/
# Edit notebook: PROJECT_TYPE="ENEL", PROJECT_NAME="Castro"
jupyter notebook ground_removal.ipynb
```

### **Day 2: Alignment**
```bash
# Download CAD data
aws s3 sync s3://ftp-enel/montalto_di_castro-italy/2025-01-30/CAD/ data/ENEL/Castro/raw/
aws s3 sync s3://ftp-enel/mudejar-spain/ data/ENEL/Mudjar/raw/

# Run alignment
cd notebooks/alignment/
# Edit notebook: PROJECT_TYPE="ENEL", PROJECT_NAME="Castro"
jupyter notebook ifc_pc_alignment_research.ipynb
```

### **Validation Commands**
```bash
# Check outputs exist
ls -la output/ENEL/Castro/ground_segmentation/
ls -la output/ENEL/Castro/pile_detection/

# Validate file formats
python3 -c "
import pandas as pd
import open3d as o3d

# Check pile detection CSV
df = pd.read_csv('output/ENEL/Castro/pile_detection/Castro_detected_pile_centers.csv')
print(f'Detected piles: {len(df)}')

# Check point cloud
pcd = o3d.io.read_point_cloud('output/ENEL/Castro/ground_segmentation/Castro_non_ground_points.ply')
print(f'Non-ground points: {len(pcd.points):,}')
"
```

---

## 📈 **Expected Results**

### **Ground Segmentation**
- **Output Files**: `{Project}_non_ground_points.ply`, `{Project}_ground_points.ply`
- **Ground Ratio**: 30-70% of total points
- **Processing Time**: 5-15 minutes per project
- **File Sizes**: 50-200 MB per output file

### **Pile Detection**
- **Output Files**: `{Project}_detected_pile_centers.csv`
- **Detection Count**: 10-100 piles per project (varies by site)
- **Confidence Scores**: >0.5 for most detections
- **Processing Time**: 5-20 minutes per project

### **Compliance Analysis**
- **Output Files**: `{Project}_compliance_analysis.csv`, `{Project}_compliance_deviations.json`
- **Compliance Rate**: >70% overall expected
- **Spatial Deviations**: <2m for most piles
- **Processing Time**: 2-10 minutes per project

### **Visualization**
- **Output Files**: `{Project}_3d_point_cloud_overlay.html`, `{Project}_pipeline_summary.html`
- **File Sizes**: 10-50 MB for interactive HTML files
- **PNG Images**: High-resolution summary images
- **Processing Time**: 5-15 minutes per project

---

## 🚨 **Troubleshooting**

### **Common Issues**

#### **Data Download Failures**
```bash
# Check AWS credentials
aws configure list
aws s3 ls s3://bucket-name/

# Check network connectivity
curl -I "https://test-url"
```

#### **Memory Issues**
```python
# Reduce point cloud size for testing
import numpy as np
import open3d as o3d

pcd = o3d.io.read_point_cloud("large_file.las")
indices = np.random.choice(len(pcd.points), len(pcd.points)//2, replace=False)
pcd_small = pcd.select_by_index(indices)
o3d.io.write_point_cloud("small_file.ply", pcd_small)
```

#### **Notebook Execution Issues**
```bash
# Clear outputs and retry
jupyter nbconvert --clear-output notebook.ipynb
jupyter nbconvert --execute notebook.ipynb

# Check JSON syntax
python3 -c "import json; json.load(open('notebook.ipynb'))"
```

### **Performance Optimization**
- **RAM**: 16GB minimum, 32GB recommended
- **Storage**: 100GB minimum for all data
- **CPU**: 4+ cores recommended
- **GPU**: Optional, helps with deep learning stages

---

## 📞 **Support**

### **Daily Check-ins**
- **9:00 AM**: Review daily tasks and data requirements
- **12:00 PM**: Progress check and issue resolution
- **5:00 PM**: Daily wrap-up and next day preparation

### **Issue Reporting**
1. Check `pipeline_testing.log` for error details
2. Document specific error messages and context
3. Include system specifications and data sizes
4. Report to testing team lead with reproduction steps

### **Success Criteria**
- ✅ All 6 projects processed successfully
- ✅ All pipeline stages validated
- ✅ Performance benchmarks met
- ✅ Quality thresholds achieved
- ✅ Complete documentation delivered

---

**🎯 Goal**: Validate the complete Energy Inspection 3D pipeline across 6 real-world projects in 7 days, ensuring robustness, performance, and quality for production deployment.
